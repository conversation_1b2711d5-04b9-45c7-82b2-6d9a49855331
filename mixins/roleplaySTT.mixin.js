'use strict';

const audioUtils = require('../services/roleplay/roleplaysessions/audioUtils');
const { AUDIO_PROCESSING_DEFAULTS } = require('../constants/constant');

module.exports = {
  name: 'roleplaySTTMixin',
  methods: {
    async _handleStudentAudioChunk(state, audioChunk, format, socket) {
      state.chunkQueue.push({ audioChunk, format });
      if (state.isHandlingChunk) return;
      state.isHandlingChunk = true;
      try {
        while (state.chunkQueue.length > 0) {
          const { audioChunk: currentAudioChunk, format: currentFormat } = state.chunkQueue.shift();
          if (currentFormat) {
            state.clientAudioFormat = {
              sampleRate: currentFormat.sampleRate || state.clientAudioFormat.sampleRate,
              channels: currentFormat.channels || state.clientAudioFormat.channels,
              bitDepth: currentFormat.bitsPerSample || state.clientAudioFormat.bitDepth,
            };
          }
          state.allAudioChunksForSession.push(currentAudioChunk);
          await this.processVoiceActivityAndStreamSTT(state, currentAudioChunk, socket);
        }
      } catch (err) {
        this.logger.error(`_handleStudentAudioChunk error for session ${state.sessionId}:`, err);
        if (socket) socket.emit('server:error', { message: 'Lỗi xử lý audio chunk phía server.' });
      } finally {
        state.isHandlingChunk = false;
      }
    },

    async processVoiceActivityAndStreamSTT(state, audioChunk, socket) {
      try {
        state.audioBuffer = Buffer.concat([state.audioBuffer, audioChunk]);
        const { sampleRate, channels, bitDepth } = state.clientAudioFormat;
        const windowSamples = (state.sherpaVad.config?.sileroVad?.windowSize) || audioUtils.VAD_DEFAULTS.windowSize;
        const frameBytes = windowSamples * (bitDepth / 8) * channels;
        while (state.audioBuffer.length >= frameBytes) {
          const frame = state.audioBuffer.slice(0, frameBytes);
          state.audioBuffer = state.audioBuffer.slice(frameBytes);
          const floatFrame = audioUtils.pcm16ToFloat32(frame);
          state.sherpaVad.acceptWaveform(floatFrame);
          while (!state.sherpaVad.isEmpty()) {
            const seg = state.sherpaVad.front();
            const buf = audioUtils.float32ToPcm16(seg.samples);
            if (buf.length > 0) {
              if (!state.isStudentSpeaking) {
                state.isStudentSpeaking = true;
                state.currentStudentAudioChunks = [];
                if (state.isAiResponding) {
                  state.isAiInterruptedByStudent = true;
                  if (socket) socket.emit('server:ai_interrupted', { sessionId: state.sessionId });
                }
                const { streamId } = await this.broker.call('roleplay.speechprocessing.initializeSpeechStream', {
                  language: 'vi-VN', sessionId: state.sessionId
                });
                state.sttStreamId = streamId;
              }
              state.currentStudentAudioChunks.push(buf);
              if (state.sttStreamId) {
                await this.broker.call('roleplay.speechprocessing.pushAudioToStream', {
                  streamId: state.sttStreamId, audioChunk: buf
                });
              }
              state.lastVoiceActivity = Date.now();
            }
            state.sherpaVad.pop();
          }
        }
        if (state.isStudentSpeaking && Date.now() - state.lastVoiceActivity > AUDIO_PROCESSING_DEFAULTS.silenceThreshold) {
          if (state.sttStreamId) {
            await this.broker.call('roleplay.speechprocessing.closeSpeechStream', { streamId: state.sttStreamId });
            state.sttStreamId = null;
          }
          state.isStudentSpeaking = false;
          state.audioBuffer = Buffer.alloc(0);
        }
      } catch (err) {
        this.logger.error(`processVoiceActivityAndStreamSTT error for session ${state.sessionId}:`, err);
        if (state.sttStreamId) await this.broker.call('roleplay.speechprocessing.closeSpeechStream', { streamId: state.sttStreamId });
        state.isStudentSpeaking = false;
        state.audioBuffer = Buffer.alloc(0);
        if (socket) socket.emit('server:error', { message: 'Lỗi xử lý giọng nói của bạn.' });
      }
    },
  }
};
