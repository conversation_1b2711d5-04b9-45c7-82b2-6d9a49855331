const {getConfig} = require("../config/config");
const config = getConfig(process.env.NODE_ENV);

const USER_FEEDBACK = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Feedback Clickee</title>
</head>

<body>
    <h1>Nội dung:</h1>
    <ul>
        <li><b>Người dùng:</b> {userEmail}</li>
        <li><b>Tên liên hệ:</b> {fullName}</li>
        <li><b>Số điện thoại:</b> {phone}</li>
        <li><b>Tính năng:</b> {feature}</li>
        <li><b>Thời gian sử dụng:</b> {time}</li>
        <li><b><PERSON><PERSON><PERSON> độ hài lòng:</b> {satisfaction}</li>
        <li><b><PERSON><PERSON><PERSON> độ hữu ích:</b> {helpfulness}</li>
        <li><b><PERSON>ố<PERSON> nâng cấp:</b> {upgrade}</li>
        <li><b>Ý kiến:</b> {opinion}</li>
    </ul>
</body>

</html>
`;
const SUPPORT_BUSINESS = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hỗ trợ khách hàng doanh nghiệp</title>
</head>

<body>
    <h1>Nội dung:</h1>
    <ul>
        <li><b>Tên liên hệ:</b> {fullName}</li>
        <li><b>Số điện thoại:</b> {phone}</li>
        <li><b>Email:</b> {email}</li>
        <li><b>Tên doanh nghiệp:</b> {company}</li>
    </ul>
</body>

</html>
`;
const TRANSACTION_TEMPLATE = `<!DOCTYPE html>
<html lang="vi">
  <head>
  <meta charset="UTF-8">
  <title>Thanh toán hóa đơn thành công</title>
<style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f4f6fb;
      margin: 0;
      padding: 0;
    }
    .email-container {
      background-color: #ffffff;
      max-width: 600px;
      margin: 30px auto;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.05);
      border-top: 6px solid #0A1E5E;
    }
    .header {
      text-align: center;
      padding-bottom: 20px;
    }
    .header img {
      width: 120px;
      margin-bottom: 10px;
    }
    .header h2 {
      color: #0A1E5E;
      margin-top: 0;
    }
    .content {
      color: #333;
      font-size: 16px;
      line-height: 1.6;
    }
    .content strong {
      color: #0A1E5E;
    }
    .invoice-details {
      background-color: #eef1f7;
      padding: 15px;
      border-radius: 6px;
      margin-top: 15px;
    }
    .invoice-details p {
      margin: 6px 0;
    }
    .button {
      display: inline-block;
      background-color: #0A1E5E;
      color: #ffffff;
      padding: 12px 24px;
      text-decoration: none;
      border-radius: 5px;
      margin-top: 20px;
    }
    .footer {
      text-align: center;
      margin-top: 30px;
      font-size: 14px;
      color: #999999;
    }
  </style>
</head>
<body>
<div class="email-container">
  <div class="header">
    <h2>Thanh toán thành công</h2>
  </div>
  <div class="content">
    <p>Xin chào <strong>{userName}</strong>,</p>
    <p>Chúng tôi xin xác nhận rằng bạn đã thanh toán thành công hóa đơn cho gói dịch vụ <strong>#{packageName}</strong> vào ngày <strong>{paymentDate}</strong>.</p>

    <div class="invoice-details">
      <p><strong>Gói dịch vụ:</strong> {packageName}</p>
      <p><strong>Ngày thanh toán:</strong> {paymentDate}</p>
      <p><strong>Phương thức:</strong> {paymentMethod}</p>
      <p><strong>Số tiền:</strong> {totalAmount}</p>
    </div>

    <p>Nếu bạn có bất kỳ thắc mắc nào, vui lòng liên hệ với chúng tôi qua email bên dưới.</p>
  </div>

  <div class="footer">
    <p>Cảm ơn bạn đã tin tưởng sử dụng dịch vụ.</p>
    <p>Email: <EMAIL></p>
  </div>
</div>
</body>
</html>
`

module.exports = {
  methods: {
    createTransactionEmail(replacements) {
      return this.replacement(TRANSACTION_TEMPLATE, replacements);
    },
    createFeedBackEmail(replacements) {
      return this.replacement(USER_FEEDBACK, replacements);
    },
    createSupportBusinessEmail(replacements) {
      return this.replacement(SUPPORT_BUSINESS, replacements);
    },
    replacement(template, replacements) {
      return template.replace(
        /{(\w+)}/g,
        (placeholderWithDelimiters, placeholderWithoutDelimiters) =>
          replacements.hasOwnProperty(placeholderWithoutDelimiters) ?
            replacements[placeholderWithoutDelimiters] : placeholderWithDelimiters
      );
    }

  }
};
