const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {PROJECT, TEMPLATE, USER, ORGANIZATION, IMAGE, FOLDER} = require("../../constants/dbCollections");

const schema = new Schema(
  {
    projectId: {type: Schema.Types.ObjectId, required: true, ref: PROJECT},
    folderId: {type: Schema.Types.ObjectId, ref: FOLDER},
    projectDetail: {type: Schema.Types.Mixed},
    imageId: {type: Schema.Types.ObjectId, ref: IMAGE},
    userId: {type: Schema.Types.ObjectId, ref: USER},
    organizationId: {type: Schema.Types.ObjectId, ref: ORGANIZATION},
    name: {type: String},
    type: {type: String, enum: ["PERSONAL", "ORGANIZATION", "SYSTEM"]},
    examType: {type: String, enum: ["EXAM_SCHOOL", "EXAM_IELTS"]},
    projectType: {type: String, enum: ["EXAM_SCHOOL", "EXAM_IELTS", "MARK_TEST_SCHOOL", "MARK_TEST_IELTS", "NORMAL"]},
    folderCode: {type: String},
    description: {type: String},
    showOnWelcome: {type: Boolean, default: false},
    isFavorite: {type: Boolean, default: false},
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(TEMPLATE, schema, TEMPLATE)
