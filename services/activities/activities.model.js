const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { ACTIVITIES, USER, ORGANIZATION, FOLDER, FILE, PROJECT, RESOURCES } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  userId: { type: Schema.Types.ObjectId, required: true, ref: USER },
  organizationId: { type: Schema.Types.ObjectId, required: true, ref: ORGANIZATION },
  action: {
    type: String,
    enum: ["create", "update", "remove", "share", "unshare", "download", "copy", "move", "upload"],
  },
  metadata: { type: Schema.Types.Mixed },
  folderId: { type: Schema.Types.ObjectId, ref: FOLDER },
  projectId: { type: Schema.Types.ObjectId, ref: PROJECT },

  isDeleted: { type: Boolean, default: false },

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.index({ userId: 1 });
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(ACTIVITIES, schema, ACTIVITIES);

