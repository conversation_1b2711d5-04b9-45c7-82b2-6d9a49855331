const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { TRACKING, USER } = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: USER },
    actionName: { type: String },
    browser: { type: String },
    ipAddress: { type: String },
    lang: { type: String },
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(TRACKING, schema, TRACKING);
