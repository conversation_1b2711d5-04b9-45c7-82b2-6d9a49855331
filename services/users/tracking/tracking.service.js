'use strict';

const Model = require('./tracking.model');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const {WHITE_LIST} = require('./tracking.constants');
const User = require('../users.model');
const AuthRole = require('../../../mixins/authRole.mixin');
const {USER_CODES} = require('../../../constants/constant');

/**
 *
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: 'trackings',
  mixins: [Db<PERSON>ong<PERSON><PERSON>(Model), BaseService, FunctionsCommon, AuthRole],
  /**
   * Settings
   */
  settings: {
    // Validator for the `create` & `insert` actions.
    entityValidator: {},
    populates: {
      userId: 'users.get',
    },
    populateOptions: ['userId'],
  },
  hooks: {
    before: {
      '*': 'checkPermissionActions',
    },
  },
  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    createTracking: {
      rest: {
        method: 'POST',
        path: '/manager',
      },
      async handler(ctx) {
        try {
          const ips = ctx.meta.client_ip;
          const userId = ctx.params.userId || ctx.meta.user?._id;
          ctx.params.ipAddress = ips;
          return await this.adapter.insert({...ctx.params, userId});
        } catch (e) {
          console.log(e);
        }
      },
    },
    getTracking: {
      rest: {
        method: 'GET',
        path: '/manager',
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {email, fullName, isDeveloper, onlyActiveUser, createdDateType, createdAtStart, createdAtEnd} =
            ctx.params;
          if (!ctx.params.sort) ctx.params.sort = '-lastVisit';
          if (createdDateType) {
            ctx.params.time = createdDateType;
            ctx.params.fromDate = createdAtStart;
            ctx.params.toDate = createdAtEnd;
          }

          const queryTime = this.extractQueryTime(ctx.params);
          const paramsList = this.extractParamsList(ctx.params);
          const page = parseInt(paramsList.page);
          const pageSize = parseInt(paramsList.pageSize);
          const sort = paramsList.sort;

          let sortAggregate = {[sort]: 1};
          if (sort.indexOf('-') !== -1) {
            sortAggregate = {[sort.split('-')[1]]: -1};
          }
          const [trackingDataRes, projectDataRes, inputDataRes, speakingDataRes, dictationShadowingDataRes] = await Promise.allSettled([
            this.reportTracking({createdAt: queryTime.createdAt}),
            ctx.call('projects.statisticProject', {query: queryTime}),
            ctx.call('inputs.statisticInputTracking', {
              query: {
                createdAt: queryTime.createdAt,
                userId: {$exists: true},
              },
            }),
            this.getSpeakingCostData({createdAt: queryTime.createdAt}),
            this.getDictationShadowingData({createdAt: queryTime.createdAt}),
          ]);

          const projectData = projectDataRes.status === 'fulfilled' ? projectDataRes.value : [];
          const inputData = inputDataRes.status === 'fulfilled' ? inputDataRes.value : [];
          const trackingData = trackingDataRes.status === 'fulfilled' ? trackingDataRes.value : [];
          const speakingData = speakingDataRes.status === 'fulfilled' ? speakingDataRes.value : [];
          const dictationShadowingData = dictationShadowingDataRes.status === 'fulfilled' ? dictationShadowingDataRes.value : [];
          const queryUser = {
            isDeleted: false,
            active: true,
            ...(isDeveloper && {isDeveloper: isDeveloper === 'true' ? true : {$ne: true}}),
            ...(fullName ? {fullName: {$regex: fullName, $options: 'i'}} : {}),
            ...(email ? {email: {$regex: email, $options: 'i'}} : {}),
            ...(createdDateType ? queryTime : {}),
          };

          const pagination = {
            page: page,
            pageSize: pageSize,
            sortAggregate: sortAggregate,
          };

          const userIds = await User.distinct('_id', queryUser);
          const userIdsSet = new Set(userIds.map(item => item.toString()));

          // Calculate Daily Active Users (DAU) and Monthly Active Users (MAU)
          const allUserVisited = trackingData.reduce(
            (acc, item) => {
              if (item._id && userIdsSet.has(item._id.toString())) {
                acc.dau += item.visitDaysNumber;
                acc.mau.add(item._id.toString());
              }
              return acc;
            },
            {dau: 0, mau: new Set()},
          );

          const {$gte, $lte} = queryTime.createdAt;
          const totalDay = Math.ceil(
            (queryTime.time === 'custom' ? $lte - $gte : new Date() - $gte) / (1000 * 60 * 60 * 24),
          );

          const mau = allUserVisited.mau.size;
          const dau = allUserVisited.dau / totalDay;

          const [userList] = await this.trackingUserList(
            queryUser,
            trackingData,
            projectData,
            inputData,
            speakingData,
            dictationShadowingData,
            pagination,
            onlyActiveUser,
          );

          const subscriptions = await ctx.call('subscriptions.find', {
            query: {isDeleted: false, status: 'ACTIVE'},
            fields: [
              '_id',
              'customerId.userId',
              'packageId.name',
              'packageId.features',
              'packageId.prices',
              'startDate',
              'endDate',
              'unitPrice',
            ],
          });

          const mapSubscriptions = subscriptions.reduce((map, subscription) => {
            if (subscription.customerId) {
              const {userId} = subscription.customerId;
              map[userId] = {
                packageId: subscription.packageId,
                startDate: subscription.startDate,
                endDate: subscription.endDate,
                unitPrice: subscription.unitPrice,
              };
            }
            return map;
          }, {});

          const feedBacks = await ctx.call('submitFeedbacks.find', {
            query: {
              userId: {$in: userIds},
            },
            fields: ['_id', 'userId'],
          });

          const feedBackIds = feedBacks.map(feedback => feedback._id);
          const userFeedbacks = await ctx.call('userFeedbacks.find', {
            query: {
              submitFeedbackId: {$in: feedBackIds},
            },
            fields: ['_id', 'feedbackId', 'projectId', 'rating', 'bool', 'comment', 'submitFeedbackId'],
          });

          const mapUserFeedback = userFeedbacks.reduce((map, userFeedback) => {
            if (userFeedback.submitFeedbackId) {
              const submitFeedbackId = userFeedback.submitFeedbackId;
              const feedbackId = userFeedback.feedbackId;
              const submitValue = `${feedbackId?.localization?.name?.vi} : ${userFeedback.comment || userFeedback.rating || userFeedback.bool}`
              if (!map[submitFeedbackId]) {
                map[submitFeedbackId] = [submitValue];
              } else {
                map[submitFeedbackId].push(submitValue);
              }
            }
            return map;
          }, {});

          feedBacks.forEach(feedBack => {
            feedBack.userFeedbacks = mapUserFeedback[feedBack._id]?.sort()?.join('\n');
          });

          const mapFeedBacks = feedBacks.reduce((map, feedback) => {
            if (feedback.userId?._id) {
              const userId = feedback.userId?._id;
              map[userId] = feedback.userFeedbacks;
            }
            return map;
          }, {});
          // console.log('feedBacks', feedBacks);
          userList.rows.forEach(user => {
            user.feedback = mapFeedBacks[user._id];
            user.subscription = mapSubscriptions[user._id];
          });

          return {
            users: userList,
            dau,
            mau,
          };
        } catch (e) {
          console.log(e);
        }
      },
    },
    downloadTracking: {
      rest: {
        method: 'GET',
        path: '/manager/download',
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          ctx.params.page = 1;
          ctx.params.limit = 1000000;
          const data = await this.actions.getTracking(ctx.params);
          console.log('getTracking=========', data.users.rows.length);
          // Create Excel file
          const excel = require('exceljs');
          const dayjs = require('dayjs');
          const workbook = new excel.Workbook();
          const worksheet = workbook.addWorksheet('User Tracking');

          // Define columns
          worksheet.columns = [
            {header: 'Full Name', key: 'fullName', width: 30},
            {header: 'Email', key: 'email', width: 30},
            {header: 'Phone', key: 'phone', width: 20},
            {header: 'hearAboutUs', key: 'hearAboutUs', width: 20},
            {header: 'Subscription', key: 'subscription', width: 20},
            {header: 'Created At', key: 'createdAt', width: 20},
            {header: 'Last Visit', key: 'lastVisit', width: 20},
            {header: 'Number of Visits', key: 'visitNumber', width: 15},
            {header: 'Number of Days Visited', key: 'visitDaysNumber', width: 20},
            {header: 'Number of Projects', key: 'numberOfProjects', width: 20},
            {header: 'Number of Submits', key: 'numberOfSubmits', width: 20},
            {header: 'Dictation Submits', key: 'dictationSubmits', width: 20},
            {header: 'Shadowing Submits', key: 'shadowingSubmits', width: 20},
            {header: 'Total Cost', key: 'totalCost', width: 15},
            {header: 'Dictation Cost', key: 'dictationTotalCost', width: 15},
            {header: 'Shadowing Cost', key: 'shadowingTotalCost', width: 15},
            {header: 'Is Developer', key: 'isDeveloper', width: 15},
            {header: 'Feedback', key: 'feedback', width: 55},
            // {header: 'IP Address', key: 'ipAddress', width: 20},
          ];

          // Format date columns using dayjs
          const dateFormat = date => {
            if (!date || date === 0) return '';
            return dayjs(date).format('YYYY/MM/DD HH:mm');
          };

          // Add data rows
          data.users.rows.forEach(user => {
            worksheet.addRow({
              ...user,
              subscription: user.subscription?.packageId?.name || '',
              createdAt: dateFormat(user.createdAt),
              lastVisit: dateFormat(user.lastVisit),
              totalCost: Math.round(user.totalCost * 1000) / 1000,
              dictationTotalCost: Math.round((user.dictationTotalCost || 0) * 1000) / 1000,
              shadowingTotalCost: Math.round((user.shadowingTotalCost || 0) * 1000) / 1000,
              phone: user.phone || '',
              hearAboutUs: user.hearAboutUs,
              feedback: user.feedback,
            });
          });

          // Style the header row
          worksheet.getRow(1).font = {bold: true};

          // Set response headers for file download
          ctx.meta.$responseHeaders = {
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition': `attachment; filename=user-tracking-${dayjs().format('YYYY-MM-DD')}.xlsx`,
          };

          // Write to buffer and return
          const buffer = await workbook.xlsx.writeBuffer();
          return buffer;
        } catch (error) {
          console.log(error);
          throw error;
        }
      },
    },
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
    reportTracking(query) {
      return Model.aggregate([
        {$match: query},
        {
          $project: {
            createdAt: {
              $dateToString: {
                date: '$createdAt',
                format: '%Y-%m-%d',
                timezone: '+07:00',
              },
            },
            userId: 1,
            updatedAt: 1,
            ipAddress: 1,
          },
        },
        {
          $group: {
            _id: '$userId',
            visitNumber: {
              $sum: 1,
            },
            visitDays: {
              $addToSet: '$createdAt',
            },
            lastVisit: {$max: '$updatedAt'},
            ipAddress: {$last: '$ipAddress'},
          },
        },
        {
          $project: {
            userId: '$_id',
            visitNumber: 1,
            lastVisit: 1,
            ipAddress: 1,
            visitDaysNumber: {
              $function: {
                body: function (days) {
                  return days.length;
                },
                args: ['$visitDays'],
                lang: 'js',
              },
            },
          },
        },
      ]);
    },

    async trackingUserList(queryUser, trackingData, projectData, inputData, speakingData, dictationShadowingData, pagination, onlyActiveUser) {
      const {page, pageSize, sortAggregate} = pagination;
      delete queryUser.isDraft;
      return User.aggregate([
        {$match: queryUser},
        {
          $addFields: {
            trackings: {
              $first: {
                $filter: {
                  input: trackingData,
                  as: 'tracking',
                  cond: {$eq: ['$$tracking._id', '$_id']},
                },
              },
            },
            projects: {$first: {$filter: {input: projectData, as: 'project', cond: {$eq: ['$$project._id', '$_id']}}}},
            inputs: {$first: {$filter: {input: inputData, as: 'input', cond: {$eq: ['$$input._id', '$_id']}}}},
            speaking: {
              $first: {
                $filter: {
                  input: speakingData,
                  as: 'speaking',
                  cond: {$eq: ['$$speaking._id', {$toString: '$_id'}]}
                }
              }
            },
            dictationShadowing: {
              $first: {
                $filter: {
                  input: dictationShadowingData,
                  as: 'dictationShadowing',
                  cond: {$eq: ['$$dictationShadowing._id', {$toString: '$_id'}]}
                }
              }
            },
          },
        },
        {
          $project: {
            fullName: 1,
            hearAboutUs: 1,
            email: 1,
            _id: 1,
            isDeveloper: 1,
            createdAt: 1,
            phone: 1,
            lastVisit: {$ifNull: ['$trackings.lastVisit', 0]},
            visitNumber: {$ifNull: ['$trackings.visitNumber', 0]},
            visitDaysNumber: {$ifNull: ['$trackings.visitDaysNumber', 0]},
            ipAddress: {$ifNull: ['$trackings.ipAddress', 'NA']},
            numberOfProjects: {
              $add: [
                {$ifNull: ['$projects.numberProjects', 0]},
                {$ifNull: ['$speaking.numberSubmit', 0]}
              ]
            },
            numberOfSubmits: {
              $add: [
                {$ifNull: ['$inputs.numberSubmit', 0]},
                {$ifNull: ['$speaking.numberSubmit', 0]},
                {$ifNull: ['$dictationShadowing.totalSubmits', 0]}
              ]
            },
            toolUsed: {
              $concatArrays: [
                {$ifNull: ['$inputs.toolUsed', []]},
                {
                  $cond: {
                    if: {$gt: [{$ifNull: ['$speaking.numberSubmit', 0]}, 0]},
                    then: [{
                      toolName: "Speaking room",
                      toolUsageCount: {$ifNull: ['$speaking.numberSubmit', 0]},
                      toolCost: {$ifNull: ['$speaking.totalCost', 0]}
                    }],
                    else: []
                  }
                },
                {
                  $cond: {
                    if: {$gt: [{$ifNull: ['$dictationShadowing.dictationSubmits', 0]}, 0]},
                    then: [{
                      toolName: "Dictation",
                      toolUsageCount: {$ifNull: ['$dictationShadowing.dictationSubmits', 0]},
                      toolCost: {$ifNull: ['$dictationShadowing.dictationTotalCost', 0]}
                    }],
                    else: []
                  }
                },
                {
                  $cond: {
                    if: {$gt: [{$ifNull: ['$dictationShadowing.shadowingSubmits', 0]}, 0]},
                    then: [{
                      toolName: "Shadowing",
                      toolUsageCount: {$ifNull: ['$dictationShadowing.shadowingSubmits', 0]},
                      toolCost: {$ifNull: ['$dictationShadowing.shadowingTotalCost', 0]}
                    }],
                    else: []
                  }
                }
              ]
            },
            totalCost: {
              $add: [
                {$ifNull: ['$inputs.totalCost', 0]},
                {$ifNull: ['$speaking.totalCost', 0]},
                {$ifNull: ['$dictationShadowing.dictationTotalCost', 0]},
                {$ifNull: ['$dictationShadowing.shadowingTotalCost', 0]}
              ]
            },
            // Speaking Room data
            speakingSubmits: {$ifNull: ['$speaking.numberSubmit', 0]},
            speakingTotalCost: {$ifNull: ['$speaking.totalCost', 0]},
            speakingSessionCost: {$ifNull: ['$speaking.sessionCost', 0]},
            speakingAnswerCost: {$ifNull: ['$speaking.answerCost', 0]},
            // Dictation and Shadowing data
            dictationSubmits: {$ifNull: ['$dictationShadowing.dictationSubmits', 0]},
            shadowingSubmits: {$ifNull: ['$dictationShadowing.shadowingSubmits', 0]},
            dictationTotalCost: {$ifNull: ['$dictationShadowing.dictationTotalCost', 0]},
            shadowingTotalCost: {$ifNull: ['$dictationShadowing.shadowingTotalCost', 0]},
          },
        },
        ...(onlyActiveUser === 'true' ? [{$match: {visitNumber: {$gt: 0}}}] : []),
        {$sort: sortAggregate},
        {
          $facet: {
            rows: [{$skip: (page - 1) * pageSize}, {$limit: pageSize}],
            metadata: [
              {$count: 'total'},
              {
                $addFields: {
                  page: page,
                  pageSize: pageSize,
                  totalPages: {$ceil: {$divide: ['$total', pageSize]}},
                },
              },
            ],
          },
        },
        {$replaceRoot: {newRoot: {$mergeObjects: ['$$ROOT', {$arrayElemAt: ['$metadata', 0]}]}}},
        {$unset: 'metadata'},
      ]);
    },

    async getSpeakingCostData(query) {
      try {
        // Lấy thông tin về giá của model
        const modelInfo = await this.broker.call("gptmodelprice.findOne", {gptModel: "gpt-4o-mini"});

        // Lấy sessions trong khoảng thời gian với status completed
        const sessionQuery = {
          isDeleted: false,
          status: 'completed',
          ...query,
        };

        const sessions = await this.broker.call("spksessions.find", {query: sessionQuery});

        if (!sessions || sessions.length === 0) {
          return [];
        }

        const sessionIds = sessions.map(session => session._id);
        // Lấy tất cả answers cho các sessions này
        console.time(21)
        const allAnswers = await this.broker.call("spkanswers.find", {
          query: {sessionId: {$in: sessionIds}},
          fields: [
            '_id',
            'sessionId',
            'userId',
            'duration',
            'gptModel',
            'completionTokens',
            'promptTokens',
            'totalTokens',
            'audioDuration',
          ]
        });
        console.timeEnd(21)

        // Tính toán chi phí cho từng session và answer
        const sessionCostMap = {};
        const answerCostMap = {};

        for (const session of sessions) {
          sessionCostMap[session._id.toString()] = this.calculateCostForSession(session, modelInfo);
        }

        for (const answer of allAnswers) {
          answerCostMap[answer._id.toString()] = this.calculateCostForAnswer(answer);
        }

        // Group sessions và answers theo userId
        const userSpeakingMap = {};

        sessions.forEach(session => {
          const userId = session.userId._id.toString();
          if (!userSpeakingMap[userId]) {
            userSpeakingMap[userId] = {
              sessions: [],
              answers: [],
            };
          }
          userSpeakingMap[userId].sessions.push(session);
        });

        allAnswers.forEach(answer => {
          const userId = answer.userId.toString();
          if (userSpeakingMap[userId]) {
            userSpeakingMap[userId].answers.push(answer);
          }
        });

        // Tính toán dữ liệu speaking cho từng user
        const result = Object.keys(userSpeakingMap).map(userId => {
          const userData = userSpeakingMap[userId];
          const userSessions = userData.sessions || [];
          const userAnswers = userData.answers || [];

          const sessionCost = userSessions.reduce((total, session) => {
            return total + (sessionCostMap[session._id.toString()] || 0);
          }, 0);

          const answerCost = userAnswers.reduce((total, answer) => {
            return total + (answerCostMap[answer._id.toString()] || 0);
          }, 0);

          return {
            _id: userId,
            numberSubmit: userSessions.length,
            totalCost: sessionCost + answerCost,
            sessionCost: sessionCost,
            answerCost: answerCost,
          };
        });

        console.log(`Calculated speaking cost for ${result.length} users`);
        return result;
      } catch (error) {
        console.error("Error getting speaking cost data:", error);
        return [];
      }
    },

    calculateCostForAnswer(answer, modelPrice = 1.3 / 60) {
      return Number(answer.duration) * modelPrice / 60 || 0;
    },

    calculateCostForSession(session, modelInfo) {
      const {priceInput, priceOutput} = modelInfo;
      return (Number(session.completionTokens) * priceOutput + Number(session.promptTokens) * priceInput) / 1e6 || 0;
    },

    async getDictationShadowingData(query) {
      try {
        // Call the statistics method from exercisesubmissions service
        const dictationShadowingData = await this.broker.call("exercisesubmissions.statisticDictationShadowingTracking", {
          query: query
        });

        console.log(`Calculated dictation/shadowing data for ${dictationShadowingData.length} users`);
        return dictationShadowingData;
      } catch (error) {
        console.error("Error getting dictation/shadowing data:", error);
        return [];
      }
    },
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
