"use strict";

const Model = require("./waitlist.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");

/**
 *
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "waitlist",
  mixins: [<PERSON>b<PERSON>ong<PERSON><PERSON>(Model), BaseService, FunctionsCommon, AuthRole],
  /**
   * Settings
   */
  settings: {
    // Validator for the `create` & `insert` actions.
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },
  hooks: {},
  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    remove: {
      rest: {
        method: "DELETE",
        path: "/:id",
      },
      permission: "admin",
      async handler(ctx) {
        const {id} = ctx.params;
        const waitListEmail = await this.adapter.findById({_id: id, isDeleted: false});
        if (!waitListEmail) throw new MoleculerClientError(i18next.t("email_not_found"), 404, "NOT_FOUND");

        return await this.adapter.removeById(id);
      },
    },
    getAll: {
      rest: {
        method: "GET",
        path: "/manager",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {time, email, fullName} = ctx.params;

          const query = {email, fullName};
          if (time) {
            query.createdAt = this.extractQueryTime(ctx.params);
          }
          const paramsList = this.extractParamsList(ctx.params);
          const params = {
            ...paramsList,
            searchFields: "email,fullName",
            query: JSON.stringify(query),
          }
          return ctx.call("waitlist.list", params);
        } catch (e) {
          console.log(e);
        }
      },
    },
    updateWhiteList: {
      rest: {
        method: "PUT",
        path: "/:id/whiteList",
      },
      permission: "admin",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {id} = ctx.params;
        const waitListEmail = await this.adapter.findById({_id: id, isDeleted: false});
        if (!waitListEmail) throw new MoleculerClientError(i18next.t("email_not_found"), 404, "NOT_FOUND");

        const whiteListEmail = await ctx.call("whitelist.getOne", {query: {email: waitListEmail.email}});
        if (whiteListEmail) throw new MoleculerClientError(i18next.t("email_in_whitelist"));

        await ctx.emit("user.activeStateUser", {email: waitListEmail.email});
        await this.adapter.removeById(waitListEmail._id);
        return await ctx.call("whitelist.create", {email: waitListEmail.email});
      }
    },
    getOne: {
      rest: {
        method: "GET",
        path: "/getOne",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {params} = ctx;
          return await this.adapter.findOne(params)
        } catch (e) {
          console.log(e);
        }
      }
    },
  },

  /**
   * Events
   */
  events: {
    "jobCreateUser": {
      async handler(ctx) {
        const [waitLists, setting, totalUser] = await Promise.all([
          this.adapter.find({query: {isDeleted: false}}),
          this.broker.call('settings.getOne'),
          this.broker.call("users.count")
        ]);
        if (waitLists.length === 0 || totalUser > setting.limitedNumberUser) return;

        const availableSlots = Math.min(setting.limitedNumberUser - totalUser, waitLists.length);

        await Promise.all(waitLists.slice(0, availableSlots).map(async (item) => {
          await ctx.emit("user.activeStateUser", {email: item.email});
          return this.adapter.removeById(item._id);
        }));
      }
    }
  },

  /**
   * Methods
   */
  methods: {},

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
