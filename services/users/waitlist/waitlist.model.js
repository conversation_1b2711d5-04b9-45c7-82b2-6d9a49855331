const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {WAIT_LIST} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    email: {
      type: String,
      trim: true,
      unique: true,
      index: true,
      lowercase: true,
      required: true
    },
    fullName: {
      type: String,
      trim: true,
      required: true
    },
    isDeleted: { type: Boolean, default: false },
    requiredResetPassword: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(WAIT_LIST, schema, WAIT_LIST);
