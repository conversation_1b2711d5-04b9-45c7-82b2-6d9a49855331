"use strict";

const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
module.exports = {
  name: "bs",
  mixins: [FunctionsCommon],

  settings: {},

  dependencies: [],
  hooks: {
    before: {
      "*": "checkAPIKey",
    }
  },
  actions: {
    register: {
      rest: {
        method: "POST",
        path: "/register",
      },
      async handler(ctx) {
        const {phone, packageCode} = ctx.params;
        //Check phone
        const {users, packages} = await this.getUserAndPackage(phone, packageCode);
        if (packages.length === 0) return {message: i18next.t("package_not_exist"), code: 3}

        const prices = packages[0].prices[0];
        const expiredDate = new Date() + Number(prices.intervalCount || 1) * 24 * 60 * 60 * 1000;
        const mt = `<PERSON>uy khach da dang ky thanh cong goi ${packageCode} cua VinaPhone (${prices.unitAmount}${prices.currency}/${prices.intervalCount}${prices.unitName}), co uu dai ${packages.clickeeData}. Goi cuoc su dung den ${this.formatDate(expiredDate)}. De huy goi, soan HUY ${packageCode} gui 888. CSKH: 18001091 (0d). Tran trong!`;
        if (users.length > 0) {
          // Đăng ký gói mới cho người dùng
          const result = await this.broker.call("subscriptions.orderFromVNPT", {
            user: users[0],
            packageInfo: packages[0]
          });

          return {message: i18next.t("register_success"), code: 0, mt}
        }

        const password = '123456';
        const user = await this.broker.call("users.registerFromPhone", {
          phone,
          password,
          packageId: packages[0]._id,
          // type: packages[0].customerTargets
        });
        user.password = password
        return {message: i18next.t("register_success"), code: 0, password, mt, account: phone}
      },
    },
    unregister: {
      rest: {
        method: "POST",
        path: "/unregister",
      },
      async handler(ctx) {
        const {phone, packageCode} = ctx.params;
        //Check phone
        const {users, packages} = await this.getUserAndPackage(phone, packageCode);
        const customers = await this.broker.call("customers.getOneByUser", {userId: users[0]._id})
        const subscriptions = (await this.broker.call("subscriptions.find", {
          query: {
            customerId: customers._id,
            packageId: packages[0]._id
          }
        })).sort((a, b) => b.endDate - a.endDate);
        let mt = ''
        if (users.length === 0) return {message: i18next.t("phone_not_registered"), code: 2}

        if (packages.length === 0) return {message: i18next.t("package_not_exist"), code: 3}
        mt = `Yeu cau huy goi ${packageCode} khong thanh cong do thue bao cua quy khach chua dang ky goi ${packageCode}. Vui bam *091#OK de biet thong tin uu dai danh rieng cho thue bao cua Quy khach. CSKH: 18001091 (0d). Tran trong!`;
        if (subscriptions.length === 0) return {message: i18next.t("package_not_registered"), mt, code: 3}

        const result = await this.broker.call("subscriptions.unActive", {
          userId: users[0]._id,
          packageId: packages[0]._id
        });
        mt = `Quy khach da huy goi cuoc ${packageCode} thanh cong. Quy khach khong duoc mien phi su dung cac dich vu noi dung di kem cua goi ${packageCode}. De su dung lai goi cuoc ${packageCode}, vui long son tin DK ${packageCode} gui 888. CSKH: 18001091 (0d). Cam on Quy khach da tin tuong su dung dich vu cua VinaPhone.`;
        return {message: i18next.t("unregister_success"), mt, code: 0}
      },
    },
    renew: {
      rest: {
        method: "POST",
        path: "/renew",
      },
      async handler(ctx) {
        const {phone, packageCode} = ctx.params;
        const {users, packages} = await this.getUserAndPackage(phone, packageCode);
        //Check phone
        if (users.length === 0) return {message: i18next.t("phone_not_registered"), code: 2}
        //Check packageCode
        if (packages.length === 0) return {message: i18next.t("package_not_exist"), code: 3}
        const customers = await this.broker.call("customers.getOneByUser", {userId: users[0]._id})
        const subscriptions = (await this.broker.call("subscriptions.find", {
          query: {
            customerId: customers._id,
            packageId: packages[0]._id
          }
        })).sort((a, b) => b.endDate - a.endDate);
        if (subscriptions.length === 0) return {message: i18next.t("package_not_registered"), code: 3}

        const subscription = await this.broker.call("subscriptions.renewPackage", {subscriptions: subscriptions[0]});
        if (!subscription) return {message: i18next.t("renew_package_error"), code: 2}
        const prices = packages[0].prices[0];
        const expiredDate = new Date(subscription.endDate) + Number(prices.intervalCount) * 24 * 60 * 60 * 1000;
        const mt = `Quy khach gia han thanh cong goi ${packageCode} cua VinaPhone (${prices.unitAmount}${prices.currency}/${prices.intervalCount}${prices.unitName}), co uu dai ${packages[0].clickeeData}. Goi cuoc su dung den ${this.formatDate(expiredDate)}. De huy goi, soan HUY ${packageCode} gui 888. CSKH: 18001091 (0d). Tran trong!`;
        return {message: i18next.t("renew_package_success"), code: 0, mt}
      },
    },

  },

  events: {},

  methods: {
    formatDate(date) {
      const d = new Date(date);
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const month = String(d.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
      const year = d.getFullYear();

      return `${hours}:${minutes}, ${day}/${month}/${year}`;
    },

    async checkAPIKey(ctx) {
      const {clickeeAPIKey} = ctx.meta;
      const setting = await ctx.call("settings.findOne");
      if (setting.clickeeAPIKey !== clickeeAPIKey) throw new MoleculerClientError(i18next.t("invalid_api_key"), 401);
    },

    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },
    async getUserAndPackage(phone, packageCode) {
      const users = await this.broker.call("users.find", {
        query: {phone}
      });

      const packages = await this.broker.call("packages.find", {
        query: {code: packageCode}
      });
      return {users, packages};
    }

  },

  created() {
  },

  async started() {
  },

  async stopped() {
  },
};
