const axios = require('axios');
const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./googleForms.model");
const BaseService = require("../../mixins/baseService.mixin");
const {MoleculerClientError} = require("moleculer").Errors;
const google = require('@googleapis/forms');
const {getConfig} = require("../../config/config");
const {ACCESS_CODE} = require("../../constants/constant");
const i18next = require("i18next");
const config = getConfig(process.env.NODE_ENV);
const AuthRole = require("../../mixins/authRole.mixin");

module.exports = {
  name: "googleforms",
  mixins: [DbMongoose(Model), BaseService, AuthRole],
  settings: {
    JWT_SECRET: process.env.JWT_SECRET || "jwt-tradar-secret",
  },
  hooks: {
    before: {
      async createForm(ctx) {
        const permission = await ctx.call("permissions.getOne", {userId: ctx.meta.user._id});
        if (permission && !permission.accessRole[ACCESS_CODE.EXPORT_TO_GG_FORM].value) {
          throw new MoleculerClientError(i18next.t("upgrade_your_plan"), 406);
        }
      }
    },
  },
  actions: {
    authGoogleForm: {
      rest: "POST /auth",
      auth: "required",
      async handler(ctx) {
        const {code} = ctx.params;
        const userId = ctx.meta.user._id;
        const {id_token, access_token, refresh_token} = await this.getOauthGoogleToken(code);

        const googleUser = await this.getGoogleUser(id_token, access_token);

        if (!googleUser.verified_email) {
          throw new MoleculerClientError(i18next.t("google_email_not_verified"), 401);
        }

        const oauth = await this.adapter.findOne({userId});
        const updateData = {accessToken: access_token};

        if (oauth) {
          await this.adapter.updateById(oauth._id, updateData);
        } else {
          const insertData = {
            accessToken: access_token,
            email: googleUser.email,
            userId,
            authorizationCode: code,
            refreshToken: refresh_token
          };
          await this.adapter.insert(insertData);
        }

        return {accessToken: access_token};
      }
    },
    createForm: {
      rest: "POST /createForm",
      auth: "required",
      async handler(ctx) {
        const userId = ctx.meta.user._id;
        const data = ctx.params;
        const oauth = await this.adapter.findOne({userId});
        console.log(oauth);
        if (!oauth) {
          throw new MoleculerClientError(i18next.t("no_refresh_token"), 403);
        }
        const {refreshToken} = oauth;
        const oauth2Client = this.getNewOAuth2Client();

        if (!refreshToken) {
          throw new MoleculerClientError(i18next.t("no_refresh_token"), 403);
        }

        const {access_token} = await this.getAccessTokenByRefreshToken(refreshToken);
        oauth2Client.setCredentials({access_token});

        const forms = google.forms({version: 'v1', auth: oauth2Client});

        // Create the form
        try {
          const response = await forms.forms.create({requestBody: this.newFormInfo(data)});
          const formUpdate = await forms.forms.batchUpdate({
            formId: response.data.formId,
            requestBody: this.getUpdateRequest(data?.output)
          });

          return {...formUpdate.data, formId: response.data.formId};
        } catch (e) {
          console.log(e, "Error=====>");
          if (e?.status === 401) {
            throw new MoleculerClientError(i18next.t("no_refresh_token"), 403);
          }
          return {success: false, error: e, code: 400};
        }

      }
    },
  },
  methods: {
    newFormInfo(data) {
      return {
        info: {
          title: "Answer the questions",
          documentTitle: data.headline || "Answer the questions",
        }
      };
    },

    getNewOAuth2Client() {
      return new google.auth.OAuth2({
        clientId: config.form_client_id,
        clientSecret: config.form_client_secret,
        redirectUri: config.form_redirect_uri,
      });
    },
    async getGoogleUser(id_token, access_token) {
      try {
        const {data} = await axios.get(
          'https://www.googleapis.com/oauth2/v1/userinfo',
          {
            params: {
              access_token,
              alt: 'json'
            },
            headers: {
              Authorization: `Bearer ${id_token}`
            }
          }
        );
        return data;
      } catch (e) {
        console.log("=====>", e);
      }
    },
    async getOauthGoogleToken(code) {
      const body = {
        code,
        client_id: config.form_client_id,
        client_secret: config.form_client_secret,
        redirect_uri: config.form_redirect_uri,
        grant_type: 'authorization_code',
        access_type: 'offline',
        prompt: 'consent',
      };
      try {
        const {data} = await axios.post(
          'https://oauth2.googleapis.com/token',
          body,
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          }
        );
        return data;
      } catch (e) {
        console.log("=====>", e);
      }
    },

    async getAccessTokenByRefreshToken(refreshToken) {
      const body = {
        client_id: config.form_client_id,
        client_secret: config.form_client_secret,
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      };
      const {data} = await axios.post(
        'https://oauth2.googleapis.com/token',
        body,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );
      return data;
    },
    getUpdateRequest({ questions, correctAnswers }) {
      const requests = [{
        updateSettings: {
          settings: {
            quizSettings: {
              isQuiz: true,
            },
          },
          updateMask: 'quizSettings.isQuiz',
        },
      }];

      questions.forEach((question, index) => {
        const options = question.options.map(option => ({ value: option.text }));
        const correctAnswer = correctAnswers[index]?.correctAnswer?.toUpperCase();
        const answers = options.find(option => option.value.toUpperCase() === correctAnswer)?.value || question.options[0]?.text;

        requests.push({
          createItem: {
            item: {
              title: `${index + 1}. ${question.question}`,
              questionItem: {
                question: {
                  required: true,
                  grading: {
                    pointValue: 1,
                    correctAnswers: {
                      answers: [{ value: answers }],
                    },
                  },
                  choiceQuestion: {
                    type: "RADIO",
                    options: options,
                    shuffle: true,
                  },
                },
              },
            },
            location: { index: index },
          },
        });
      });

      return { requests };
    }
  },

};
