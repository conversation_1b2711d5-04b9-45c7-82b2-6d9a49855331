const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { FILE, VIDEO, IMAGE, AUDIO, RESOURCES, USER, ORGANIZATION, OFFLINE_VIDEOS } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  name: { type: String },
  videoId: { type: mongoose.Schema.Types.ObjectId, ref: VIDEO },
  offlineVideoId: { type: mongoose.Schema.Types.ObjectId, ref: OFFLINE_VIDEOS },
  imageId: { type: mongoose.Schema.Types.ObjectId, ref: IMAGE },
  audioId: { type: mongoose.Schema.Types.ObjectId, ref: AUDIO },
  fileId: { type: mongoose.Schema.Types.ObjectId, ref: FILE },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: USER },
  organizationId: { type: mongoose.Schema.Types.ObjectId, ref: ORGANIZATION },
  type: {
    type: String,
    enum: ['VIDEO', 'IMAGE', 'AUDIO', 'DOCUMENT'],
  },
  text: { type: String },

  isDeleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(RESOURCES, schema, RESOURCES);

