const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./resources.model");
const BaseService = require("../../mixins/baseService.mixin");
const FileMixin = require("../../mixins/file.mixin");
const path = require("path");
const fs = require("fs");
const {SERVICE_NAME} = require("./resources");
const {FILE_SERVICE} = require("../File");
const {IMAGE_SERVICE} = require("../Image");
const i18next = require("i18next");
const {INPUT_TYPE} = require("../../constants/constant");
const wav = require("wav");
const storageDir = path.join(__dirname, "storage");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");

module.exports = {
  name: SERVICE_NAME,
  mixins: [DbMongoose(Model), BaseService, FileMixin, AuthRole],
  settings: {
    populates: {
      "videoId": "videos.get",
      "imageId": "images.get",
      "audioId": "audios.get",
      "fileId": "files.get",
      "offlineVideoId": "offlinevideos.get",
      "userId": "users.get",
      "organizationId": "organizations.get",
    },
    populateOptions: ["videoId", "imageId.imageFileId", "imageId", "audioId.audioFileId", "fileId", "userId", "organizationId", "offlineVideoId.videoFileId"],
  },
  hooks: {
    before: {
      async upload(ctx) {
        const {userId, organizationId} = ctx.meta.$multipart;

        const organization = await ctx.call("organizations.getOne", {id: organizationId});
        if (organization?.active === false) {
          throw new MoleculerClientError(i18next.t("organization_already_locked"), 423);
        }

        const permission = await ctx.call("permissions.getOne", {userId, organizationId});
        if (permission && permission.accessLimit.capacityUsed >= +permission.accessLimit.capacityLimit) {
          throw new MoleculerClientError(i18next.t("upgrade_your_plan"), 406, "FORBIDDEN");
        }
      },
      "remove|createVideo": "checkPermission",
      async list(ctx) {
        const {userId} = ctx.params
        const {user} = ctx.meta;
        if(user._id != userId){
          throw new MoleculerClientError(i18next.t("error_permission_denied"), 403);
        }
      }
    },
    after: {
      "*": "activityLogger",
    }
  },

  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.NORMAL
    },
    upload: {
      activityLogger: true,
      async handler(ctx) {
        const {type, userId, organizationId, fileType} = ctx.meta.$multipart;
        const resourceObject = {userId, organizationId};
        let newResource;
        switch (type) {
          case "IMAGE":
            ctx.meta.$multipart.folder = "images";
            const imageData = await ctx.call(IMAGE_SERVICE.upload, ctx.params, {meta: ctx.meta});
            newResource = await this.createImageResource(imageData?._id.toString(), resourceObject, "IMAGE");
            return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, newResource);

          case "AUDIO":
            ctx.meta.$multipart.folder = "audio";
            const audioData = await ctx.call("files.upload", ctx.params, {meta: ctx.meta});
            await ctx.call("audios.audioTranscript", {audioId: audioData._id.toString()});
            newResource = await this.createAudioResource(audioData._id.toString(), resourceObject);
            return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, newResource);

          case "VIDEO":
            ctx.meta.$multipart.folder = "video";
            const videoData = await ctx.call("offlinevideos.upload", ctx.params, {meta: ctx.meta});
            newResource = await this.createOfflineVideoResource(videoData._id.toString(), resourceObject);
            return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, newResource);

          case "DOCUMENT":
            const fileExtension = this.getFileExtension(ctx.meta.filename).toLowerCase();
            if (["pdf"].includes(fileExtension)) {
              ctx.meta.$multipart.folder = "office";
              const file = await ctx.call("files.upload", ctx.params, {meta: ctx.meta});
              newResource = await this.createDocumentResource(file, resourceObject);
              return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, newResource);
            } else if (["jpg", "jpeg", "png"].includes(fileExtension)) {
              ctx.meta.$multipart.folder = "images";
              const {image, text} = await ctx.call("images.imageToText", ctx.params, {meta: ctx.meta});
              resourceObject.text = text;
              newResource = await this.createImageResource(image?._id?.toString(), resourceObject, "DOCUMENT");
              return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, newResource);
            } else {
              throw new MoleculerClientError(i18next.t("resource_not_supported"), 400);
            }

          default:
            throw new MoleculerClientError(i18next.t("resource_not_supported"), 400);
        }
      }
    },
    createVideo: {
      auth: "required",
      rest: "POST /video",
      activityLogger: true,
      async handler(ctx) {
        const {url, userId, organizationId} = ctx.params;
        const video = await ctx.call("videos.videoDetail", {url});
        if (!video) {
          throw new MoleculerClientError(i18next.t("error_video_not_found"), 404);
        }
        const resource = await this.adapter.findOne({videoId: video._id, userId, organizationId});

        if (resource) {
          throw new MoleculerClientError(i18next.t("resource_already_exists"), 400);
        }
        let resourceObj = {
          userId,
          organizationId,
          videoId: video._id,
          type: "VIDEO",
          name: video.title
        };
        const newResource = await this.adapter.insert(resourceObj);
        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, newResource);
      }
    },
    available: {
      auth: "required",
      rest: "GET /available",
      async handler(ctx) {
        const {user} = ctx.meta;
        const resources = await ctx.call("resources.find", {query: {userId: user._id, isDeleted: false}});
        if (user.organizationId) {
          const orgResources = await ctx.call("resources.find", {
            query: {
              organizationId: user.organizationId,
              isDeleted: false
            }
          });
          return [...resources, ...orgResources];
        }
        return resources;
      }
    },

    checkExists: {
      auth: "required",
      rest: "GET /:id/check",
      async handler(ctx) {
        const {id} = ctx.params;
        const resource = await ctx.call("resources.get", {id, populate: this.settings.populateOptions});
        if (resource.imageId) {
          return await ctx.call("files.checkExists", {id: resource.imageId?.thumbnailFileId, folder: "images"});
        }
        if (resource.fileId) {
          return await ctx.call("files.checkExists", {id: resource.fileId?._id, folder: "office"});
        }
        if (resource.audioId) {
          return await ctx.call("files.checkExists", {id: resource.audioId?.audioFileId, folder: "audio"});
        }
        if (!resource) {
          throw new MoleculerClientError(i18next.t("error_resource_not_found"), 404);
        }
        return resource;
      }
    },

    capacity: {
      rest: "GET /capacity",
      async handler(ctx) {
        const {organizationId, userId} = ctx.params;
        const resources = await this.broker.call("resources.find", {
          query: {organizationId, userId, isDeleted: false}
        });

        const userPermissionPromise = ctx.call("permissions.getOne", {userId});
        const orgPermissionPromise = ctx.call("permissions.getOne", {organizationId});

        const [userPermission, orgPermission, audioSizes, documentSizes, imageSizes, videoSizes] = await Promise.all([
          userPermissionPromise,
          orgPermissionPromise,
          this.getAudiosSize(resources.filter(resource => resource.audioId)),
          this.getDocumentsSize(resources.filter(resource => resource.fileId)),
          this.getImagesSize(resources.filter(resource => resource.imageId)),
          this.getVideosSize(resources.filter(resource => resource.offlineVideoId)),
        ]);

        const capacityLimit = userId ? userPermission?.accessLimit?.capacityLimit : orgPermission?.accessLimit?.capacityLimit;

        return {
          audioSizes: +audioSizes.toFixed(2),
          documentSizes: +documentSizes.toFixed(2),
          imageSizes: +imageSizes.toFixed(2),
          videoSizes: +videoSizes.toFixed(2),
          capacityLimit
        };
      }
    },

    remove: {
      rest: "DELETE /:id",
      activityLogger: true,
      role: USER_CODES.NORMAL,
      async handler(ctx) {
        try {
          const {id} = ctx.params;
          const resource = await this.adapter.updateById(id, {isDeleted: true});
          const resourceObject = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, resource);
          ctx.emit("resourceDeleted", {resource: resourceObject});

          return resource;
        } catch (e) {
          throw new MoleculerClientError(i18next.t("error_resource_not_found"), 404);
        }
      }
    },

    createFromRecordAudio: {
      auth: "required",
      rest: "POST /audio",
      activityLogger: true,
      async handler(ctx) {
        try {
          const {audioChunks, fileName, userId} = ctx.params;
          const file = await this.broker.call("files.createFileRecordAudio", {audioChunks, fileName, userId});
          await ctx.call("audios.audioDetail", {audioId: file._id.toString()});
          const newResource = await this.createAudioResource(file._id.toString(), {userId});
          return file
        } catch (e){
          throw new MoleculerClientError(i18next.t("error_resource_not_found"), 404);
        }
      }
    }

  },
  methods: {
    async checkPermission(ctx) {
      const {user} = ctx.meta;
      if(user?.isSystemAdmin) return;

      let {organizationId, id} = ctx.params;
      if(id) {
        const resource = await this.adapter.findById(id);
        const hasPermission =
          (resource.userId && resource.userId.toString() === user._id.toString()) ||
          (resource.organizationId && resource.organizationId.toString() === user.organizationId.toString());

        if (!hasPermission) {
          throw new MoleculerClientError(i18next.t("error_permission_denied"), 403);
        }

        organizationId = resource?.organizationId;
      }

      const organization = await ctx.call("organizations.getOne", {id: organizationId});
      if (organization?.active === false) {
        throw new MoleculerClientError(i18next.t("organization_already_locked"), 423);
      }
    },
    activityLogger(context, res) {
      const {action, params} = context;
      if (action.activityLogger && (params?.organizationId || res?.organizationId?._id || res?.organizationId)) {
        let actionType = action.rawName;
        if (actionType === "createVideo") actionType = "upload";

        context.emit("activities.logger", {
          metadata: {
            type: res?.type,
            name: res?.name
          },
          isDeleted: res?.isDeleted,
          action: actionType
        })
      }
      return res
    },
    async getAudiosSize(audios) {
      const audioSizes = audios.map(audio => {
        return audio.audioId?.audioFileId?.size || 0;
      });
      return audioSizes.reduce((totalSize, currentSize) => totalSize + currentSize / (1024 * 1024), 0);
    },
    async getDocumentsSize(documents) {
      const documentSizes = documents.map(document => {
        return document.fileId?.size || 0;
      });
      return documentSizes.reduce((totalSize, currentSize) => totalSize + currentSize / (1024 * 1024), 0);
    },

    async getImagesSize(images) {
      const imageSizes = images.map(image => {
        return image.imageId?.imageFileId?.size || 0;
      });
      return imageSizes.reduce((totalSize, currentSize) => totalSize + currentSize / (1024 * 1024), 0);
    },
    async getVideosSize(videos) {
      const videoSizes = videos.map(image => {
        return image.offlineVideoId?.videoFileId?.size || 0;
      });
      return videoSizes.reduce((totalSize, currentSize) => totalSize + currentSize / (1024 * 1024), 0);
    },

    getStoragePath() {
      return storageDir;
    },

    async createVideoResource(videoId, resourceObject) {
      const {userId, organizationId} = resourceObject;
      const video = await this.broker.call("videos.getOne", {videoId: videoId});
      const resourceObj = {
        ...resourceObject,
        videoId: video._id,
        type: "VIDEO",
        name: video.title
      };
      const resource = await this.adapter.findOne({videoId: video._id, userId, organizationId});
      if (!resource) {
        return await this.adapter.insert(resourceObj);
      }
      return resource;
    },

    async createOfflineVideoResource(offlineVideoId, resourceObject) {
      const {userId, organizationId} = resourceObject;
      const offlineVideo = await this.broker.call("offlinevideos.get", {id: offlineVideoId});
      const resourceObj = {
        ...resourceObject,
        offlineVideoId: offlineVideo._id,
        type: "VIDEO",
        name: offlineVideo.name
      };
      const resource = await this.adapter.findOne({offlineVideoId, userId, organizationId});
      if (!resource) {
        return await this.adapter.insert(resourceObj);
      }
      return resource;
    },
    async createImageResource(imageId, resourceObject, type) {
      const {userId, organizationId} = resourceObject;
      const image = await this.broker.call("images.get", {id: imageId});

      const resourceObj = {
        ...resourceObject,
        imageId,
        type,
        name: image.name
      };
      const resource = await this.adapter.findOne({imageId, userId, organizationId});
      if (!resource) {
        return await this.adapter.insert(resourceObj);
      }
      return resource;
    },
    async createAudioResource(audioId, resourceObject) {
      const {userId, organizationId} = resourceObject;
      const file = await this.broker.call("files.get", {id: audioId});
      const audio = await this.broker.call("audios.getOne", {audioFileId: audioId});

      const resourceObj = {
        ...resourceObject,
        audioId: audio._id,
        type: "AUDIO",
        name: file.displayName
      };
      const resource = await this.adapter.findOne({audioId: audio._id, userId, organizationId});
      if (!resource) {
        return await this.adapter.insert(resourceObj);
      }
      return resource;
    },
    async createDocumentResource(file, resourceObject) {
      const {userId, organizationId} = resourceObject;
      const resourceObj = {
        ...resourceObject,
        fileId: file._id,
        type: "DOCUMENT",
        name: file.displayName
      };
      const resource = await this.adapter.findOne({fileId: file._id, userId, organizationId});
      if (!resource) {
        return await this.adapter.insert(resourceObj);
      }
      return resource;
    }
  },

  events: {
    resourceUpdate: {
      params: {
        projectId: "object",
        inputData: "object",
        inputType: "string",
      },
      async handler(ctx) {

        const {inputData, inputType, projectId} = ctx.params;
        const project = await ctx.call("projects.get", {id: projectId.toString()});
        const workspace = await ctx.call("workspaces.get", {id: project.workspaceId.toString()});
        let resourceObject = {};
        if (workspace.type === "PERSONAL") {
          resourceObject.userId = workspace.userId;
        } else {
          resourceObject.organizationId = workspace.organizationId;
        }
        switch (inputType) {
          case INPUT_TYPE.MARK_TEST_IMAGE:
          case INPUT_TYPE.MARK_TEST_TASK_1:
          case INPUT_TYPE.STUDENT_TASK_1:
            return this.createImageResource(inputData.topicImageId, resourceObject, "IMAGE");
          case INPUT_TYPE.IMAGE:
            return this.createImageResource(inputData.imageId || inputData.topicImageId, resourceObject, "IMAGE");
          case INPUT_TYPE.VIDEO:
            if (inputData.videoType === "offline") {
              return this.createOfflineVideoResource(inputData.offlineVideoId, resourceObject);
            }
            return this.createVideoResource(inputData.videoId, resourceObject);
          case INPUT_TYPE.OFFLINE_VIDEO:
            return this.createOfflineVideoResource(inputData.offlineVideoId, resourceObject);
          case INPUT_TYPE.AUDIO:
            return this.createAudioResource(inputData.audioId, resourceObject);
          case INPUT_TYPE.TEXT:
            if (!!inputData.imageFileId) {
              const image = await ctx.call("images.find", {query: {imageFileId: inputData.imageFileId}});
              resourceObject.text = image[0]?.text;
              return this.createImageResource(image[0]?._id, resourceObject, "DOCUMENT");
            }
            if (!!inputData.fileId) {
              const file = await ctx.call("files.get", {id: inputData.fileId});
              return this.createDocumentResource(file, resourceObject);
            }
            return {success: false};
          default:
            return {success: false};
        }
      }
    },
    offlineVideoUploaded: {
      async handler(ctx) {
        const {offlineVideo, userId, organizationId} = ctx.params;
        let resourceObject = {userId, organizationId};
        const resourceObj = {
          ...resourceObject,
          offlineVideoId: offlineVideo._id,
          type: "VIDEO",
          name: offlineVideo.name
        };
        const resource = await this.adapter.findOne({offlineVideoId: offlineVideo._id, userId, organizationId});
        if (!resource) {
          return await this.adapter.insert(resourceObj);
        }
      },
    },

    pdfUploaded: {
      params: {
        file: "object",
      },
      async handler(ctx) {
        const {file, userId, organizationId} = ctx.params;
        let resourceObject = {userId, organizationId};
        const resourceObj = {
          ...resourceObject,
          fileId: file._id,
          type: "DOCUMENT",
          name: file.displayName
        };
        const resource = await this.adapter.findOne({fileId: file._id});
        if (!resource) {
          return await this.adapter.insert(resourceObj);
        }
      },


    },
    imageExtracted: {
      params: {
        image: "object",
      },
      async handler(ctx) {
        const {image, text, userId, organizationId} = ctx.params;
        console.log(image, text, userId, organizationId);
        const resourceObject = {text, userId, organizationId};
        const resourceObj = {
          ...resourceObject,
          imageId: image._id,
          type: "DOCUMENT",
          name: image.name,
        };
        const resource = await this.adapter.findOne({imageId: image._id});
        if (!resource) {
          return await this.adapter.insert(resourceObj);
        }
        return resource;
      }
    },

  },
  async started() {
  },
};
