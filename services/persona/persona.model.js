const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { PERSONA, TEMPLATE, TOOL } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');
const { PERSONA_OPTIONS } = require("./permissions.constants");
const schema = new Schema({

  code: {
    type: String,
    unique: true,
    required: true,
    enum: Object.values(PERSONA_OPTIONS),
  },
  templateId: [{ type: mongoose.Schema.Types.ObjectId, ref: TEMPLATE }],
  toolId: [{ type: mongoose.Schema.Types.ObjectId, ref: TOOL }],

  isDeleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(PERSONA, schema, PERSONA);

