const difflib = require("difflib");
const sdk = require("microsoft-cognitiveservices-speech-sdk");
const _ = require("lodash");
const {getConfig} = require("../../config/config");
const config = getConfig(process.env.NODE_ENV);


module.exports.getAccuracyScore = async (params) => {
  const {audioStream, socket, callback, inputData} = params
  const {segment} = inputData
  const {serviceRegion, speechKey} = params
  const audioConfig = sdk.AudioConfig.fromStreamInput(audioStream);
  const speechConfig = sdk.SpeechConfig.fromSubscription(speechKey || config.speechKey, serviceRegion || config.serviceRegion);

  speechConfig.speechRecognitionLanguage = "en-US";

  let reference_text = segment.text;

  const pronunciationAssessmentConfig = new sdk.PronunciationAssessmentConfig(
    reference_text,
    sdk.PronunciationAssessmentGradingSystem.HundredMark,
    sdk.PronunciationAssessmentGranularity.Phoneme,
    true
  );
  pronunciationAssessmentConfig.enableProsodyAssessment = true;

  let language = "en-US"
  speechConfig.speechRecognitionLanguage = language;

  // create the speech recognizer.
  let reco = new sdk.SpeechRecognizer(speechConfig, audioConfig);
  pronunciationAssessmentConfig.applyTo(reco);

  // pronunciationAssessmentScore(socket, reco, callback, inputData, confidenceThreshold);
  const scoreNumber = {
    accuracyScore: 0,
    fluencyScore: 0,
    compScore: 0,
    prosodyScore: 0,
  };

  let premiumResults = [];
  const allWords = [];
  let currentText = [];
  let startOffset = 0;
  let recognizedWords = [];
  let fluencyScores = [];
  let prosodyScores = [];
  let durations = [];
  let jo = {};
  let recognizedText = "";

  reco.recognizing = function (s, e) {
    let str = "(recognizing) Reason: " + sdk.ResultReason[e.result.reason] + " Text: " + e.result.text;
    socket.send({state: "recognizing", recognizing: e.result.text})
  };

  reco.recognized = function (s, e) {
    let pronunciation_result = sdk.PronunciationAssessmentResult.fromResult(e.result);

    jo = JSON.parse(e.result.properties.getProperty(sdk.PropertyId.SpeechServiceResponse_JsonResult));
    recognizedText += jo.DisplayText + ' ';
    premiumResults.push(jo);
    const nb = jo["NBest"][0];
    startOffset = nb.Words[0].Offset;
    const localtext = _.map(nb.Words, (item) => item.Word.toLowerCase());
    currentText = currentText.concat(localtext);
    fluencyScores.push(nb.PronunciationAssessment.FluencyScore);
    prosodyScores.push(nb.PronunciationAssessment.ProsodyScore);
    const isSucceeded = jo.RecognitionStatus === 'Success';
    const nBestWords = jo.NBest[0].Words;
    const durationList = [];
    _.forEach(nBestWords, (word) => {
      recognizedWords.push(word);
      durationList.push(word.Duration);
    });
    durations.push(_.sum(durationList));

    if (isSucceeded && nBestWords) {
      allWords.push(...nBestWords);
    }
  };

  function calculateOverallPronunciationScore() {
    const resText = currentText.join(" ");
    let wholelyricsArry = [];
    let resTextArray = [];


    let resTextProcessed = (resText.toLocaleLowerCase() ?? "").replace(new RegExp("[!\"#$%&()*+,-./:;<=>?@[^_`{|}~]+", "g"), "").replace(new RegExp("]+", "g"), "");
    let wholelyrics = (reference_text.toLocaleLowerCase() ?? "").replace(new RegExp("[!\"#$%&()*+,-./:;<=>?@[^_`{|}~]+", "g"), "").replace(new RegExp("]+", "g"), "");
    wholelyricsArry = wholelyrics.split(" ");
    resTextArray = resTextProcessed.split(" ");

    const wholelyricsArryRes = _.map(
      _.filter(wholelyricsArry, (item) => !!item),
      (item) => item.trim()
    );

    const diff = new difflib.SequenceMatcher(null, wholelyricsArryRes, resTextArray);
    const lastWords = [];
    for (const d of diff.getOpcodes()) {
      if (d[0] === "insert" || d[0] === "replace") {
        for (let j = d[3]; j < d[4]; j++) {
          if (allWords && allWords.length > 0 && allWords[j].PronunciationAssessment.ErrorType !== "Insertion") {
            allWords[j].PronunciationAssessment.ErrorType = "Insertion";
          }
          lastWords.push(allWords[j]);
        }
      }
      if (d[0] === "delete" || d[0] === "replace") {
        if (
          d[2] === wholelyricsArryRes.length &&
          !(
            jo.RecognitionStatus === "Success" ||
            jo.RecognitionStatus === "Failed"
          )
        )
          continue;
        for (let i = d[1]; i < d[2]; i++) {
          const word = {
            Word: wholelyricsArryRes[i],
            PronunciationAssessment: {
              ErrorType: "Omission",
            },
          };
          lastWords.push(word);
        }
      }
      if (d[0] === "equal") {
        for (let k = d[3], count = 0; k < d[4]; count++) {
          lastWords.push(allWords[k]);
          k++;
        }
      }
    }

    let reference_words = [];

    reference_words = wholelyricsArryRes;

    let recognizedWordsRes = []
    _.forEach(recognizedWords, (word) => {
      if (word.PronunciationAssessment.ErrorType === "None") {
        recognizedWordsRes.push(word)
      }
    });

    let compScore = Number(((recognizedWordsRes.length / reference_words.length) * 100).toFixed(0));
    if (compScore > 100) {
      compScore = 100;
    }
    scoreNumber.compScore = compScore;

    const accuracyScores = [];
    _.forEach(lastWords, (word) => {
      if (word && word?.PronunciationAssessment?.ErrorType !== "Insertion") {
        accuracyScores.push(Number(word?.PronunciationAssessment.AccuracyScore ?? 0));
      }
    });
    scoreNumber.accuracyScore = Number((_.sum(accuracyScores) / accuracyScores.length).toFixed(0));

    if (startOffset) {
      const sumRes = [];
      _.forEach(fluencyScores, (x, index) => {
        sumRes.push(x * durations[index]);
      });
      scoreNumber.fluencyScore = _.sum(sumRes) / _.sum(durations);
    }
    scoreNumber.prosodyScore = Number((_.sum(prosodyScores) / prosodyScores.length).toFixed(0));

    const sortScore = Object.keys(scoreNumber).sort(function (a, b) {
      return scoreNumber[a] - scoreNumber[b];
    });
    if (
      jo.RecognitionStatus === "Success" ||
      jo.RecognitionStatus === "Failed"
    ) {
      scoreNumber.pronScore = Number(
        (
          scoreNumber[sortScore["0"]] * 0.4 +
          scoreNumber[sortScore["1"]] * 0.2 +
          scoreNumber[sortScore["2"]] * 0.2 +
          scoreNumber[sortScore["3"]] * 0.2
        ).toFixed(0)
      );
    } else {
      scoreNumber.pronScore = Number(
        (scoreNumber.accuracyScore * 0.5 + scoreNumber.fluencyScore * 0.5).toFixed(0)
      );
    }
    const submitData = {
      accuracyScore: scoreNumber.accuracyScore,
      ...inputData,
      premiumResults,
      transcript: recognizedText
    }
    socket.send({state: "overall_score", scoreNumber, premiumResults})
    callback(submitData)

  };

  reco.canceled = function (s, e) {
    if (e.reason === sdk.CancellationReason.Error) {
      let str = "(cancel) Reason: " + sdk.CancellationReason[e.reason] + ": " + e.errorDetails;
      console.log(str);
    }
    reco.stopContinuousRecognitionAsync();
  };

  reco.sessionStarted = function (s, e) {
  };

  // Signals the end of a session with the speech service.
  reco.sessionStopped = function (s, e) {
    reco.stopContinuousRecognitionAsync();
    reco.close();
    calculateOverallPronunciationScore();
  };

  reco.startContinuousRecognitionAsync();

}
