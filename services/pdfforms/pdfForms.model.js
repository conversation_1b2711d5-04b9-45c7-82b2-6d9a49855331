const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { PDF_FORM, USER } = require('../../constants/dbCollections');

const schema = new mongoose.Schema({
  formContent: { type: Schema.Types.Mixed },
  ownerId: { type: mongoose.Schema.Types.ObjectId, ref: USER },
  isDeleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(PDF_FORM, schema, PDF_FORM);
