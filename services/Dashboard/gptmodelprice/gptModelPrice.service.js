const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const DashboardModel = require("./gptModelPrice.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");

module.exports = {
  name: "gptmodelprice",
  mixins: [DbMongoose(DashboardModel), BaseService, FunctionsCommon, AuthRole],
  settings: {
    populates: {
      "apiKeyIds": "apikeys.get",
    },
    populateOptions: ["apiKeyIds"],
  },
  hooks: {
    before: {
      getAllWithoutPagination: async (ctx) => {
        ctx.params.sort = "-createdAt";
      }
    }
  },

  actions: {
    findOne: {
      rest: {
        method: "GET",
        path: "/findOne",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {gptModel} = ctx.params;
        const data = await this.adapter.findOne({gptModel});
        if (!data) {
          return;
        }
        const allAPIKeyData = await ctx.call("apikeys.find", {query: {_id: {$in: data?.apiKeyIds}}});
        const dataTrans = await this.transformDocuments(ctx, {}, data);
        return {...dataTrans, apiKeyIds: allAPIKeyData};
      }
    },

    getOneByGptModel: {
      rest: {
        method: "GET",
        path: "/getOneByGptModel",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {gptModel, instructionId} = ctx.params;

        if (!gptModel) {
          return;
        }

        const data = await this.adapter.findOne({gptModel});

        if (!data) {
          return;
        }
        const {apiKeyIds, maxTokens} = data;
        const allAPIKeyData = await ctx.call("apikeys.find", {query: {_id: {$in: apiKeyIds}}});
        if (!allAPIKeyData.length) {
          return;
        }

        const {apiKey, modelInterface, url} = allAPIKeyData[0];

        return {
          maxTokens,
          apiKey,
          modelInterface,
          instructionId,
          url
        };
      }
    }
  },


  methods: {}
};
