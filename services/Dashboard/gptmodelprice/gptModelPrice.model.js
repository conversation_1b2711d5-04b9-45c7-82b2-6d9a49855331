const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { GPT_MODEL_PRRICE, GPT_API_KEY } = require('../../../constants/dbCollections');
const { Schema } = require("mongoose");

const schema = new mongoose.Schema({
  tokenUnit: { type: Number },
  unit: { type: String },
  gptModel: { type: String, },
  maxTokens: { type: Number, },
  priceInput: { type: Number },
  priceOutput: { type: Number },
  modelInterface: { type: String },
  apiKeyIds: [{ type: Schema.Types.ObjectId, ref: GPT_API_KEY }],
  isDeleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(GPT_MODEL_PRRICE, schema, GPT_MODEL_PRRICE);
