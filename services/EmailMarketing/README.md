# Email Marketing System - <PERSON><PERSON> thống Email Marketing Tự động

## Tổng quan

Hệ thống Email Marketing cho phép tạo và quản lý các chiến dịch email tự động với khả năng:
- <PERSON><PERSON><PERSON> email theo lịch trình (schedule)
- Chỉ gửi email cho các nhóm người dùng được chỉ định (targetGroups)
- <PERSON>õi thống kê chi tiết
- Quản lý trạng thái chiến dịch

## Cấu trúc Services

### 1. Campaigns Service (`mktcampaigns`)
- **Chức năng**: <PERSON>u<PERSON>n lý các chiến dịch email
- **Model**: `campaigns.model.js`
- **Các trường mới**:
  - `lastSentAt`: Thời gian gửi lần cuối
  - `nextScheduledAt`: Thời gian gửi tiếp theo
  - `isScheduleActive`: Trạng thái kích hoạt schedule

### 2. Scheduler Service (`mktscheduler`)
- **Chứ<PERSON> năng**: <PERSON>ử lý việc gửi email tự động theo lịch trình
- **Actions**:
  - `processCampaigns`: Kiểm tra và gửi campaigns đã được schedule
  - `calculateNextSchedule`: Tính toán thời gian gửi tiếp theo

### 3. Groups Service (`mktgroups`)
- **Chức năng**: Quản lý nhóm người dùng
- **Hỗ trợ**: Automatic groups và Manual groups

### 4. Marketing Users Service (`mktusers`) - **MỚI**
- **Chức năng**: Quản lý danh sách người dùng cho email marketing
- **Tính năng**:
  - Upload file Excel (.xlsx) với validation
  - Kiểm tra duplicate email trong cùng group
  - CRUD operations cho marketing users
  - Download template Excel
  - Thống kê users theo group

### 5. Templates Service (`mkttemplates`)
- **Chức năng**: Quản lý template email
- **Hỗ trợ**: Biến động (variables) trong template

### 6. Sender Service (`mktsender`)
- **Chức năng**: Gửi email thực tế
- **Tính năng**: Tracking, click tracking, permission management

### 7. Logs Service (`mktlogs`)
- **Chức năng**: Theo dõi và log các email đã gửi
- **Thống kê**: Sent, opened, clicked, converted

## Cách thức hoạt động

### 1. Tạo Campaign
```javascript
// Tạo campaign với schedule
const campaign = await broker.call('mktcampaigns.create', {
  name: 'Welcome Email Campaign',
  description: 'Gửi email chào mừng cho người dùng mới',
  type: 'automatic', // hoặc 'manual'
  targetGroups: ['groupId1', 'groupId2'],
  templateId: 'templateId',
  schedule: {
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    frequency: 'daily', // 'once', 'daily', 'weekly', 'monthly'
    timeOfDay: '09:00',
    daysOfWeek: [1, 2, 3, 4, 5] // Thứ 2-6 (optional cho weekly)
  }
});
```

### 2. Kích hoạt Campaign
```javascript
// Cách 1: Sử dụng action activate (khuyến nghị)
await broker.call('mktcampaigns.activate', {
  id: campaignId
});

// Cách 2: Chuyển campaign sang trạng thái running
await broker.call('mktcampaigns.updateStatus', {
  id: campaignId,
  status: 'running'
});
```

### 3. Gửi ngay lập tức (Manual)
```javascript
// Gửi campaign ngay lập tức
await broker.call('mktcampaigns.sendNow', {
  id: campaignId
});
```

## Cron Job

Hệ thống sử dụng cron job chạy mỗi phút để kiểm tra campaigns cần gửi:

```javascript
{
  name: "email-campaigns-scheduler",
  cronTime: '*/1 * * * *', // Mỗi phút
  async onTick() {
    await this.broker.call('mktscheduler.processCampaigns');
  }
}
```

## Quy trình gửi email

1. **Cron job** kiểm tra campaigns có `nextScheduledAt <= now`
2. **Scheduler** lấy danh sách users từ `targetGroups`
3. **Render template** với data của từng user
4. **Gửi email** qua Sender service
5. **Tạo log** để tracking
6. **Cập nhật statistics** của campaign
7. **Tính toán** `nextScheduledAt` cho lần gửi tiếp theo

## Trạng thái Campaign

- `draft`: Nháp, chưa được kích hoạt
- `scheduled`: Đã được lên lịch
- `running`: Đang chạy
- `paused`: Tạm dừng
- `completed`: Hoàn thành
- `failed`: Thất bại

## Frequency Types

- `once`: Chỉ gửi một lần
- `daily`: Gửi hàng ngày
- `weekly`: Gửi hàng tuần
- `monthly`: Gửi hàng tháng

## Target Groups

Campaigns chỉ gửi email cho users thuộc các `targetGroups` được chỉ định:

### Automatic Groups
- `never_used_after_signup`: Người dùng mới
- `inactive_over_14_days`: Người dùng không hoạt động
- `trial_expired_no_upgrade`: Người dùng hoạt động
- `paid_plan_expiring_soon`: Gói sắp hết hạn
- `paid_plan_expired_7_days`: Gói đã hết hạn
- `feedback_upgrade_intent`: Đã sử dụng mã khuyến mãi

### Manual Groups
Dựa trên các điều kiện:
- Ngày đăng ký
- Ngày đăng ký trả phí
- Ngày hết hạn gói
- Trạng thái feedback
- Số ngày sử dụng
- Số ngày không đăng nhập
- Loại gói
- Mã khuyến mãi đã sử dụng

## API Endpoints

### Campaigns
- `POST /api/mktcampaigns` - Tạo campaign
- `PUT /api/mktcampaigns/:id/status` - Cập nhật trạng thái
- `POST /api/mktcampaigns/:id/activate` - Kích hoạt campaign (khuyến nghị)
- `POST /api/mktcampaigns/:id/send-now` - Gửi ngay lập tức
- `GET /api/mktcampaigns/:id/statistics` - Xem thống kê

### Groups
- `GET /api/mktgroups/:id/users` - Lấy users trong group

### Marketing Users - **MỚI**
- `POST /upload/marketingUsers` - Upload file Excel và import users
- `GET /api/mktusers/:mktGroupId/users` - Lấy users theo marketing group
- `GET /api/mktusers/:mktGroupId/stats` - Thống kê users trong group
- `GET /api/mktusers/template` - Download template Excel
- `DELETE /api/mktusers/:id` - Xóa user khỏi marketing group

### Templates
- `POST /api/mkttemplates/test` - Gửi email test

## Tracking & Analytics

Hệ thống tự động tracking:
- **Email sent**: Khi email được gửi thành công
- **Email opened**: Khi người dùng mở email (tracking pixel)
- **Link clicked**: Khi người dùng click link trong email
- **Conversion**: Khi người dùng thực hiện hành động mong muốn

## Lưu ý quan trọng

1. **Permission**: Scheduler service bỏ qua permission check khi gửi email
2. **Rate limiting**: Có delay 100ms giữa các email để tránh spam
3. **Error handling**: Campaigns sẽ chuyển sang `failed` nếu có lỗi
4. **Schedule calculation**: Tự động tính toán lần gửi tiếp theo dựa trên frequency
5. **End date**: Campaigns sẽ dừng khi đến `endDate`

## Ví dụ hoàn chỉnh

### Tạo và kích hoạt campaign tự động

```javascript
// 1. Tạo campaign
const campaign = await broker.call('mktcampaigns.create', {
  name: 'Welcome Email Series',
  description: 'Gửi email chào mừng cho người dùng mới hàng ngày',
  type: 'automatic',
  targetGroups: ['new_user_group_id'],
  templateId: 'welcome_template_id',
  schedule: {
    startDate: new Date(),
    endDate: new Date('2024-12-31'),
    frequency: 'daily',
    timeOfDay: '09:00'
  }
});

// 2. Kích hoạt campaign
const result = await broker.call('mktcampaigns.activate', {
  id: campaign._id
});

console.log(result.message); // "Campaign đã được kích hoạt thành công"

// 3. Kiểm tra thống kê
const stats = await broker.call('mktcampaigns.getStatistics', {
  id: campaign._id
});

console.log(stats);
// {
//   totalEmails: 150,
//   sent: 145,
//   opened: 89,
//   clicked: 23,
//   converted: 5,
//   failed: 5,
//   openRate: 61.38,
//   clickRate: 15.86,
//   conversionRate: 3.45
// }
```

## Cấu hình SMTP

Email được gửi qua nodemailer với cấu hình trong `config/config.js`:

```javascript
mail: {
  host: 'smtp.gmail.com',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'lpgvdupjzjfccxcv'
  }
}
```
