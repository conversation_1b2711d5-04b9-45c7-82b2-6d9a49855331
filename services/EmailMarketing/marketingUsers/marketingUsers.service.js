const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./marketingUsers.model');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const FileMixin = require('../../../mixins/file.mixin');
const fs = require('fs');
const path = require('path');
const os = require('os');

module.exports = {
  name: 'mktusers',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, FileMixin],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      'mktGroupId': 'mktgroups.get',
    },
  },

  hooks: {},

  actions: {
    /**
     * Upload Excel file and import marketing users
     */
    uploadExcel: {
      auth: "required",
      async handler(ctx) {
        const {mktGroupId} = ctx.meta.$multipart;

        if (!mktGroupId) {
          throw new MoleculerClientError('mktGroupId is required', 400);
        }

        // Validate file type
        const filename = ctx.meta.filename;
        const mimetype = ctx.meta.mimetype;

        if (!filename) {
          throw new MoleculerClientError('No file uploaded', 400);
        }

        const fileExtension = this.getFileExtension(filename).toLowerCase();
        const validExtensions = ['csv'];
        const validMimeTypes = [
          'text/csv',
          'application/csv',
          'text/plain'
        ];

        if (!validExtensions.includes(fileExtension) && !validMimeTypes.includes(mimetype)) {
          throw new MoleculerClientError('Only CSV files (.csv) are allowed', 400);
        }

        // Verify that the marketing group exists
        const group = await this.broker.call('mktgroups.get', {id: mktGroupId});
        if (!group) {
          throw new MoleculerClientError('Marketing group not found', 404);
        }

        // Process CSV file directly from stream/buffer
        const result = await this.processCSVFromStream(ctx.params, mktGroupId);

        return result;
      }
    },

    /**
     * Get users by marketing group ID
     */
    getUsersByGroup: {
      rest: {
        method: 'GET',
        path: '/:mktGroupId/users'
      },
      params: {
        mktGroupId: {type: 'string'},
      },
      async handler(ctx) {
        const {mktGroupId} = ctx.params;
        return await this.adapter.find({
          query: {
            mktGroupId: mktGroupId,
            isDeleted: false
          }
        });
      }
    },

    /**
     * Delete user from marketing group
     */
    deleteUser: {
      rest: {
        method: 'DELETE',
        path: '/:id'
      },
      params: {
        id: {type: 'string'}
      },
      async handler(ctx) {
        const {id} = ctx.params;

        const user = await this.adapter.findById(id);
        if (!user) {
          throw new MoleculerClientError('Marketing user not found', 404);
        }

        // Soft delete
        return await this.adapter.updateById(id, {isDeleted: true});
      }
    },

    /**
     * Get statistics for a marketing group
     */
    getGroupStats: {
      rest: {
        method: 'GET',
        path: '/:mktGroupId/stats'
      },
      params: {
        mktGroupId: {type: 'string'}
      },
      async handler(ctx) {
        const {mktGroupId} = ctx.params;

        const totalUsers = await this.adapter.count({
          mktGroupId: mktGroupId,
          isDeleted: false
        });

        return {
          mktGroupId,
          totalUsers
        };
      }
    },

    /**
     * Download CSV template for user import
     */
    downloadTemplate: {
      rest: {
        method: 'GET',
        path: '/template'
      },
      async handler(ctx) {
        // Create CSV content
        const headers = ['email', 'name', 'phone'];
        const sampleData = [
          ['<EMAIL>', 'John Doe', '+1234567890'],
          ['<EMAIL>', 'Jane Smith', '+0987654321']
        ];

        // Build CSV content
        let csvContent = headers.join(',') + '\n';
        sampleData.forEach(row => {
          csvContent += row.map(field => {
            // Escape fields that contain commas or quotes
            if (field.includes(',') || field.includes('"') || field.includes('\n')) {
              return `"${field.replace(/"/g, '""')}"`;
            }
            return field;
          }).join(',') + '\n';
        });

        // Set response headers for file download
        ctx.meta.$responseHeaders = {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename=marketing-users-template.csv'
        };

        return csvContent;
      }
    }
  },

  methods: {
    /**
     * Process CSV file from stream/buffer and import users
     */
    async processCSVFromStream(stream, mktGroupId) {
      let tempFilePath = null;

      try {
        // Create temporary file path
        const tempFileName = `temp_csv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.csv`;
        tempFilePath = path.join(os.tmpdir(), tempFileName);

        // Save stream to temporary file
        await this.saveStreamToFile(stream, tempFilePath);

        // Validate file exists and has content
        if (!fs.existsSync(tempFilePath)) {
          throw new MoleculerClientError('Failed to save uploaded file', 400);
        }

        const stats = fs.statSync(tempFilePath);
        if (stats.size === 0) {
          throw new MoleculerClientError('File is empty', 400);
        }

        // Read CSV file content
        const csvContent = fs.readFileSync(tempFilePath, 'utf8');
        const lines = csvContent.split('\n').filter(line => line.trim() !== '');

        if (lines.length < 2) {
          throw new MoleculerClientError('CSV file must contain at least one data row besides header', 400);
        }

        // Parse CSV header
        const headerLine = lines[0];
        const headerColumns = this.parseCSVLine(headerLine);
        const headers = {};

        headerColumns.forEach((column, index) => {
          const value = column.toLowerCase().trim();
          if (value === 'email') headers.email = index;
          if (value === 'name') headers.name = index;
          if (value === 'phone') headers.phone = index;
        });

        // Validate required columns exist
        if (headers.email === undefined || headers.name === undefined || headers.phone === undefined) {
          throw new MoleculerClientError(
            'CSV file must contain columns: email, name, phone',
            400
          );
        }

      const results = {
        total: 0,
        success: 0,
        failed: 0,
        duplicate: 0,
        errors: [],
        data: [] // Array chứa tất cả dữ liệu với thông tin lỗi
      };

        // Process each row (skip header)
        for (let rowNumber = 1; rowNumber < lines.length; rowNumber++) {
          const line = lines[rowNumber].trim();

          // Skip empty lines
          if (!line) continue;

          results.total++;
          const actualRowNumber = rowNumber + 1; // For user-friendly error messages

          try {
            const columns = this.parseCSVLine(line);

            const email = columns[headers.email]?.trim() || '';
            const name = columns[headers.name]?.trim() || '';
            const phone = columns[headers.phone]?.trim() || '';

            // Tạo object dữ liệu cho dòng này
            const rowData = {
              row: actualRowNumber,
              email: email || '',
              name: name || '',
              phone: phone || '',
              status: 'pending',
              error: null
            };

            // Validate required fields
            if (!email || !name || !phone) {
              rowData.status = 'failed';
              rowData.error = 'Missing required fields (email, name, or phone)';
              results.failed++;
              results.errors.push({
                row: actualRowNumber,
                error: rowData.error
              });
              results.data.push(rowData);
              continue;
            }

            // Validate email format
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
              rowData.status = 'failed';
              rowData.error = 'Invalid email format';
              results.failed++;
              results.errors.push({
                row: actualRowNumber,
                error: rowData.error
              });
              results.data.push(rowData);
              continue;
            }

            // Check for duplicate in the same group
            const existingUser = await this.adapter.findOne({
              email: email,
              mktGroupId: mktGroupId,
              isDeleted: false
            });

            if (existingUser) {
              rowData.status = 'duplicate';
              rowData.error = 'Email already exists in this marketing group';
              results.duplicate++;
              results.errors.push({
                row: actualRowNumber,
                error: rowData.error
              });
              results.data.push(rowData);
              continue;
            }

            // Insert user
            const insertedUser = await this.adapter.insert({
              email,
              name,
              phone,
              mktGroupId
            });

            rowData.status = 'success';
            rowData.error = null;
            rowData.userId = insertedUser._id;
            results.success++;
            results.data.push(rowData);

          } catch (error) {
            const columns = this.parseCSVLine(line);
            const rowData = {
              row: actualRowNumber,
              email: columns[headers.email]?.trim() || '',
              name: columns[headers.name]?.trim() || '',
              phone: columns[headers.phone]?.trim() || '',
              status: 'failed',
              error: error.message
            };

            results.failed++;
            results.errors.push({
              row: actualRowNumber,
              error: error.message
            });
            results.data.push(rowData);
          }
        }

      return results;
      } catch (error) {
        this.logger.error('Error processing Excel file:', error);
        throw new MoleculerClientError(
          `Failed to process Excel file: ${error.message}`,
          400
        );
      } finally {
        // Clean up temporary file
        if (tempFilePath && fs.existsSync(tempFilePath)) {
          try {
            fs.unlinkSync(tempFilePath);
          } catch (cleanupError) {
            this.logger.warn('Failed to cleanup temporary file:', cleanupError);
          }
        }
      }
    },

    /**
     * Save stream to file
     */
    async saveStreamToFile(stream, filePath) {
      return new Promise((resolve, reject) => {
        const writeStream = fs.createWriteStream(filePath);

        stream.pipe(writeStream);

        writeStream.on('finish', () => {
          resolve(filePath);
        });

        writeStream.on('error', (error) => {
          reject(error);
        });

        stream.on('error', (error) => {
          reject(error);
        });
      });
    },

    /**
     * Parse CSV line handling quoted fields and commas
     */
    parseCSVLine(line) {
      const result = [];
      let current = '';
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
          if (inQuotes && line[i + 1] === '"') {
            // Escaped quote
            current += '"';
            i++; // Skip next quote
          } else {
            // Toggle quote state
            inQuotes = !inQuotes;
          }
        } else if (char === ',' && !inQuotes) {
          // Field separator
          result.push(current);
          current = '';
        } else {
          current += char;
        }
      }

      // Add the last field
      result.push(current);

      return result;
    }
  },

  events: {}
};
