const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./logs.model');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const crypto = require('crypto');

module.exports = {
  name: 'mktlogs',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      'campaignId': 'mktcampaigns.get',
      'userId': 'users.get',
    },
  },

  hooks: {
    before: {
      'track': function (ctx) {
        // Skip authentication for tracking endpoints
        return ctx;
      },
      'trackClick': function (ctx) {
        // Skip authentication for tracking endpoints
        return ctx;
      },
    }
  },

  actions: {
    /**
     * Create a new email log entry
     */
    create: {
      params: {
        campaignId: {type: 'string', optional: true},
        userId: {type: 'string', optional: true},
        email: {type: 'string'},
        subject: {type: 'string', optional: true},
        content: {type: 'string', optional: true},
      },
      async handler(ctx) {
        const entity = ctx.params;

        // Generate a unique tracking ID
        entity.trackingId = this.generateTrackingId();
        entity.status = 'queued';

        const doc = await this.adapter.insert(entity);
        const log = await this.transformDocuments(ctx, {}, doc);

        return log;
      }
    },

    /**
     * Update email log status
     */
    updateStatus: {
      params: {
        id: {type: 'string'},
        status: {type: 'enum', values: ['queued', 'sent', 'failed', 'opened', 'clicked', 'converted']},
        errorMessage: {type: 'string', optional: true},
      },
      async handler(ctx) {
        const {id, status, errorMessage} = ctx.params;

        const log = await this.adapter.findById(id);
        if (!log) {
          throw new MoleculerClientError(i18next.t('Log not found'), 404);
        }

        const update = {status};

        // Add timestamp based on status
        if (status === 'opened') {
          update.openedAt = new Date();
        } else if (status === 'clicked') {
          update.clickedAt = new Date();
        } else if (status === 'converted') {
          update.convertedAt = new Date();
        }

        // Add error message if provided
        if (errorMessage) {
          update.errorMessage = errorMessage;
        }

        const updated = await this.adapter.updateById(id, {$set: update});
        const result = await this.transformDocuments(ctx, {}, updated);

        // Emit event for campaign statistics update
        this.broker.emit('email.log.status.updated', {
          logId: id,
          campaignId: log.campaignId,
          status
        });

        return result;
      }
    },

    getStatistics: {
      rest: {
        method: 'GET',
        path: '/statistics'
      },
      params: {
        campaignId: {type: 'string', optional: true},
        startDate: {type: 'string', optional: true},
        endDate: {type: 'string', optional: true},
      },
      async handler(ctx) {
        const {campaignId, startDate, endDate} = ctx.params;

        // Build query
        const query = {};
        if (campaignId) {
          query.campaignId = campaignId;
        }

        if (startDate || endDate) {
          query.createdAt = {};
          if (startDate) {
            query.createdAt.$gte = new Date(startDate);
          }
          if (endDate) {
            query.createdAt.$lte = new Date(endDate);
          }
        }

        // Get all logs matching the query
        const logs = await this.adapter.find({query});

        // Calculate statistics
        const stats = {
          total: logs.length,
          queued: logs.filter(log => log.status === 'queued').length,
          sent: logs.filter(log => log.status === 'sent').length,
          failed: logs.filter(log => log.status === 'failed').length,
          opened: logs.filter(log => log.status === 'opened').length,
          clicked: logs.filter(log => log.status === 'clicked').length,
          converted: logs.filter(log => log.status === 'converted').length,
          openRate: 0,
          clickRate: 0,
          conversionRate: 0,
        };

        if (stats.sent > 0) {
          stats.openRate = (stats.opened / stats.sent) * 100;
          stats.clickRate = (stats.clicked / stats.sent) * 100;
          stats.conversionRate = (stats.converted / stats.sent) * 100;
        }

        return stats;
      }
    },
    findOne: {
      async handler(ctx) {
        const {trackingId} = ctx.params;
        return await this.adapter.findOne({trackingId});
      }
    }
  },

  methods: {
    /**
     * Generate a unique tracking ID
     */
    generateTrackingId() {
      return crypto.randomBytes(16).toString('hex');
    },
  }
};
