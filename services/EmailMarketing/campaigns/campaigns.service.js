const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./campaigns.model');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'mktcampaigns',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      'templateId': 'mkttemplates.get',
      'targetGroups': 'mktgroups.get',
    },
    populateOptions: ['templateId', 'targetGroups'],
  },

  hooks: {},

  actions: {
    /**
     * Create a new email campaign
     */
    create: {
      params: {
        name: {type: 'string', min: 2},
        description: {type: 'string', optional: true},
        type: {type: 'enum', values: ['automatic', 'manual'], default: 'manual'},
        targetGroups: {type: 'array', items: 'string', optional: true},
        templateId: {type: 'string'},
      },
      async handler(ctx) {
        const {user} = ctx.meta;
        const entity = ctx.params;
        entity.createdBy = user._id;
        entity.status = 'draft';
        entity.statistics = {
          totalSent: 0,
          totalOpened: 0,
          totalClicked: 0,
          totalConverted: 0,
        };

        const doc = await this.adapter.insert(entity);
        const campaign = await this.transformDocuments(ctx, {}, doc);

        return campaign;
      }
    },

    /**
     * Update campaign status (pause/resume)
     */
    updateStatus: {
      rest: {
        method: 'PUT',
        path: '/:id/status'
      },
      params: {
        id: {type: 'string'},
        status: {type: 'enum', values: ['draft', 'scheduled', 'running', 'paused', 'completed', 'failed']},
      },
      async handler(ctx) {
        const {id, status} = ctx.params;

        const campaign = await this.adapter.findById(id);
        if (!campaign) {
          throw new MoleculerClientError(i18next.t('Campaign not found'), 404);
        }

        // Validate status transitions
        const validTransitions = {
          'draft': ['scheduled'],
          'scheduled': ['running', 'paused', 'draft'],
          'running': ['paused', 'completed', 'failed'],
          'paused': ['running', 'completed'],
          'completed': [],
          'failed': ['draft'],
        };

        if (!validTransitions[campaign.status].includes(status)) {
          throw new MoleculerClientError(
            `Cannot transition from ${campaign.status} to ${status}`,
            400
          );
        }

        // Xử lý các trạng thái chuyển đổi
        if (status === 'running') {
          // Nếu chuyển sang running, emit event cho scheduler
          this.broker.emit('email.campaign.started', {campaignId: id});

          // Kích hoạt schedule cho campaigns tự động
          if (campaign.type === 'automatic') {
            await this.adapter.updateById(id, {isScheduleActive: true});
          }
        }

        if (status === 'paused') {
          // Nếu chuyển sang paused, tắt schedule
          await this.adapter.updateById(id, {isScheduleActive: false});
        }

        if (status === 'completed' || status === 'failed') {
          // Nếu campaign hoàn thành hoặc thất bại, tắt schedule
          await this.adapter.updateById(id, {isScheduleActive: false});
        }

        const updated = await this.adapter.updateById(id, {$set: {status}});
        const result = await this.transformDocuments(ctx, {}, updated);

        return result;
      }
    },

    /**
     * Get campaign statistics
     */
    getStatistics: {
      rest: {
        method: 'GET',
        path: '/:id/statistics'
      },
      params: {
        id: {type: 'string'},
      },
      async handler(ctx) {
        const {id} = ctx.params;

        const campaign = await this.adapter.findById(id);
        if (!campaign) {
          throw new MoleculerClientError(i18next.t('Campaign not found'), 404);
        }

        // Get detailed statistics from logs
        const logs = await this.broker.call('mktlogs.find', {
          query: {campaignId: id},
        });

        // Calculate statistics
        const stats = {
          totalEmails: logs.length,
          sent: logs.filter(log => log.status === 'sent').length,
          opened: logs.filter(log => log.status === 'opened').length,
          clicked: logs.filter(log => log.status === 'clicked').length,
          converted: logs.filter(log => log.status === 'converted').length,
          failed: logs.filter(log => log.status === 'failed').length,
          openRate: 0,
          clickRate: 0,
          conversionRate: 0,
        };

        if (stats.sent > 0) {
          stats.openRate = (stats.opened / stats.sent) * 100;
          stats.clickRate = (stats.clicked / stats.sent) * 100;
          stats.conversionRate = (stats.converted / stats.sent) * 100;
        }

        return stats;
      }
    },

    /**
     * Gửi campaign ngay lập tức (manual)
     */
    sendNow: {
      rest: {
        method: 'POST',
        path: '/:id/sendNow'
      },
      params: {
        id: {type: 'string'}
      },
      async handler(ctx) {
        const {id} = ctx.params;

        const campaign = await this.adapter.findById(id);
        if (!campaign) {
          throw new MoleculerClientError(i18next.t('Campaign not found'), 404);
        }

        this.broker.call('mktscheduler.processOneCampaign', {campaignId: id});

        // Cập nhật trạng thái campaign
        await this.adapter.updateById(id, {
          status: 'completed',
          isScheduleActive: campaign.type === 'automatic'
        });

        // set timeout 1s
        await new Promise(resolve => setTimeout(resolve, 1000));

        return {
          message: 'Campaign đã được gửi thành công',
        };
      }
    },

    /**
     * Kích hoạt campaign (chuyển sang running và setup schedule)
     */
    activate: {
      rest: {
        method: 'POST',
        path: '/:id/activate'
      },
      params: {
        id: {type: 'string'}
      },
      async handler(ctx) {
        const {id} = ctx.params;

        const campaign = await this.adapter.findById(id);
        if (!campaign) {
          throw new MoleculerClientError(i18next.t('Campaign not found'), 404);
        }

        // Kiểm tra trạng thái campaign
        if (!['draft', 'scheduled'].includes(campaign.status)) {
          throw new MoleculerClientError(
            'Chỉ có thể kích hoạt campaigns ở trạng thái draft hoặc scheduled',
            400
          );
        }

        // Kiểm tra campaign có đầy đủ thông tin cần thiết
        if (!campaign.templateId) {
          throw new MoleculerClientError('Campaign phải có template', 400);
        }

        if (!campaign.targetGroups || campaign.targetGroups.length === 0) {
          throw new MoleculerClientError('Campaign phải có ít nhất một target group', 400);
        }

        // Cập nhật trạng thái campaign sang running
        await this.actions.updateStatus({
          id,
          status: 'running'
        }, {parentCtx: ctx});

        return {
          message: 'Campaign đã được kích hoạt thành công',
          status: 'running'
        };
      }
    },

    open: {
      rest: {
        method: "GET",
        path: "/track/:trackingId"
      },
      auth: false,
      async handler(ctx) {
        const {trackingId} = ctx.params;
        const log = await this.broker.call('mktlogs.findOne', {trackingId});
        if (!log) {
          throw new MoleculerClientError(i18next.t('Log not found'), 404);
        }
        const campaignId = log.campaignId;
        // Update campaign statistics
        const campain = await this.adapter.findById(campaignId);
        if (!campain) {
          throw new MoleculerClientError(i18next.t('Campaign not found'), 404);
        }
        const statistics = {
          ...campain.statistics,
          totalOpened: campain.statistics.totalOpened + 1
        };

        // Update log status
        await this.broker.call('mktlogs.update', {
          id: log._id,
          status: 'opened'
        });
        await this.adapter.updateById(campaignId, {statistics});

        // Trả về 1x1 transparent GIF
        ctx.meta.$responseType = "image/gif";
        ctx.meta.$responseHeaders = {
          "Content-Type": "image/gif",
          "Cache-Control": "no-cache"
        };

        return Buffer.from(
          "R0lGODlhAQABAPAAAP///wAAACwAAAAAAQABAEACAkQBADs=",
          "base64"
        );
      }
    }


  },

  methods: {},

  events: {
    /**
     * Handle campaign statistics update
     */
    'email.log.status.updated'(payload) {
      const {campaignId, status} = payload;

      if (!campaignId) return;

      // Update campaign statistics based on the log status
      const updateField = {
        'opened': 'statistics.totalOpened',
        'clicked': 'statistics.totalClicked',
        'converted': 'statistics.totalConverted',
        'sent': 'statistics.totalSent',
      };

      if (updateField[status]) {
        this.adapter.updateById(campaignId, {
          $inc: {[updateField[status]]: 1}
        });
      }
    }
  }
};
