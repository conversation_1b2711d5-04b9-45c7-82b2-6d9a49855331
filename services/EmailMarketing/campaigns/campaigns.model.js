const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {EMAIL_CAMPAIGNS, EMAIL_GROUPS, EMAIL_TEMPLATES, USER} = require('../../../constants/dbCollections');
const {CAMPAIGN_STATUS, FREQUENCY_TYPE} = require("../emailMarketing.constants");
const schema = new Schema({
  name: {type: String, required: true},
  description: {type: String},
  status: {
    type: String,
    enum: Object.values(CAMPAIGN_STATUS),
    default: CAMPAIGN_STATUS.DRAFT
  },
  type: {
    type: String,
    enum: ['automatic', 'manual'],
    default: 'manual'
  },
  targetGroups: [{type: Schema.Types.ObjectId, ref: EMAIL_GROUPS}],
  templateId: {type: Schema.Types.ObjectId, ref: EMAIL_TEMPLATES},
  schedule: {
    startDate: {type: Date},
    endDate: {type: Date},
    frequency: {
      type: String,
      enum: Object.values(FREQUENCY_TYPE),
      default: FREQUENCY_TYPE.ONCE
    },
    daysOfWeek: [{type: Number}], // 0-6 (Sunday - Saturday)
    timeOfDay: {type: String}, // Format: "HH:MM"
  },
  // Thêm các trường để theo dõi schedule
  lastSentAt: {type: Date},
  nextScheduledAt: {type: Date},
  isScheduleActive: {type: Boolean, default: false},
  statistics: {
    totalSent: {type: Number, default: 0},
    totalOpened: {type: Number, default: 0},
    totalClicked: {type: Number, default: 0},
    totalConverted: {type: Number, default: 0},
  },
  createdBy: {type: Schema.Types.ObjectId, ref: USER},
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(EMAIL_CAMPAIGNS, schema, EMAIL_CAMPAIGNS);
