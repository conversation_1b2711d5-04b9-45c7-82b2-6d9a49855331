const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const moment = require('moment');

module.exports = {
  name: 'mktscheduler',
  mixins: [BaseService, FunctionsCommon],

  settings: {},

  actions: {
    /**
     * Kiểm tra và gửi các campaigns đã được schedule
     */
    processCampaigns: {
      async handler(ctx) {
        try {
          this.logger.info('Bắt đầu x<PERSON> lý campaigns đã được schedule');

          const now = new Date();
          const today = moment().startOf('day');
          const currentHour = moment().format('HH:mm');

          // T<PERSON><PERSON> các campaigns cần gửi với điề<PERSON> kiện lọc chi tiết
          const campaigns = await this.broker.call('mktcampaigns.find', {
            query: {
              status: 'running',
              isScheduleActive: true,
              // Kiểm tra startDate <= today và endDate >= today
              $and: [
                {
                  $or: [
                    {'schedule.startDate': {$exists: false}},
                    {'schedule.startDate': null},
                    {'schedule.startDate': {$lte: today.toDate()}}
                  ]
                },
                {
                  $or: [
                    {'schedule.endDate': {$exists: false}},
                    {'schedule.endDate': null},
                    {'schedule.endDate': {$gte: today.toDate()}}
                  ]
                }
              ]
            },
            populate: ['templateId', 'targetGroups']
          });
          // Lọc thêm các campaigns theo daysOfWeek và timeOfDay
          const filteredCampaigns = campaigns.filter(campaign => {
            if (campaign.schedule?.frequency === 'weekly') {
              const daysOfWeek = campaign.schedule?.daysOfWeek || [];
              const currentDay = moment().day();
              if (!daysOfWeek.includes(currentDay)) {
                return false;
              }
            }
            // Kiểm tra timeOfDay trùng với giờ hiện tại
            const scheduleTime = campaign.schedule?.timeOfDay || '09:00';
            return scheduleTime === currentHour;
          });
          this.logger.info(`Tìm thấy ${filteredCampaigns.length} campaigns cần xử lý`);

          for (const campaign of filteredCampaigns) {
            await this.processCampaign(campaign);
          }

          return {processed: filteredCampaigns.length};
        } catch (error) {
          this.logger.error('Lỗi khi xử lý campaigns:', error);
          throw error;
        }
      }
    },
    /**
     * Kiểm tra và gửi các campaigns đã được schedule
     */
    processOneCampaign: {
      async handler(ctx) {
        try {
          const {campaignId} = ctx.params;
          const campaigns = await this.broker.call('mktcampaigns.find', {
            query: {
              _id: campaignId,
            },
            populate: ['templateId', 'targetGroups']
          });

          await this.processCampaign(campaigns[0]);
          return {processed: campaigns.length};
        } catch (error) {
          this.logger.error('Lỗi khi xử lý campaigns:', error);
          throw error;
        }
      }
    },

    /**
     * Tính toán thời gian gửi tiếp theo cho campaign
     */
    calculateNextSchedule: {
      params: {
        campaignId: {type: 'string'}
      },
      async handler(ctx) {
        const {campaignId} = ctx.params;

        const campaign = await this.broker.call('mktcampaigns.get', {id: campaignId});
        if (!campaign) {
          throw new MoleculerClientError('Campaign không tồn tại', 404);
        }

        const nextSchedule = this.calculateNextScheduleTime(campaign);

        if (nextSchedule) {
          await this.broker.call('mktcampaigns.update', {
            id: campaignId,
            nextScheduledAt: nextSchedule
          });
        }

        return {nextScheduledAt: nextSchedule};
      }
    }
  },

  methods: {
    /**
     * Xử lý một campaign cụ thể
     */
    async processCampaign(campaign) {
      try {
        this.logger.info(`Xử lý campaign: ${campaign.name} (${campaign._id})`);

        // Lấy danh sách users từ targetGroups
        const users = await this.getUsersFromTargetGroups(campaign.targetGroups);
        if (users.length === 0) {
          this.logger.warn(`Campaign ${campaign.name} không có users để gửi`);
          return;
        }

        this.logger.info(`Gửi email cho ${users.length} users`);

        // Gửi email cho từng user
        let sentCount = 0;
        let failedCount = 0;
        const userSend = users.filter(user => user.email === "<EMAIL>")
        console.log("users", users)
        console.log("userSend", userSend)
        for (const user of users) {
          try {
            await this.sendEmailToUser(campaign, user);
            sentCount++;
          } catch (error) {
            this.logger.error(`Lỗi gửi email cho user ${user.email}:`, error);
            failedCount++;
          }
        }

        // Cập nhật statistics
        await this.updateCampaignStatistics(campaign._id, sentCount, failedCount);

        // Xử lý cập nhật nextScheduledAt theo frequency
        const updateData = {
          lastSentAt: new Date()
        };

        const frequency = campaign.schedule?.frequency || 'once';
        const today = moment().startOf('day');
        const endDate = campaign.schedule?.endDate ? moment(campaign.schedule.endDate).startOf('day') : null;

        if (frequency === 'once') {
          // Với frequency "once", cập nhật status = "completed" ngay lập tức
          updateData.status = 'completed';
          updateData.isScheduleActive = false;
        } else {
          // Tính toán nextScheduledAt cho các frequency khác
          let nextScheduledAt = null;

          if (frequency === 'daily') {
            nextScheduledAt = moment().add(1, 'day').startOf('day');
          } else if (frequency === 'weekly') {
            nextScheduledAt = moment().add(7, 'days').startOf('day');
          } else if (frequency === 'monthly') {
            nextScheduledAt = moment().add(1, 'month').startOf('day');
          }

          // Kiểm tra nếu nextScheduledAt > endDate thì set status = "completed"
          if (nextScheduledAt && endDate && nextScheduledAt.isAfter(endDate)) {
            updateData.status = 'completed';
            updateData.isScheduleActive = false;
          } else if (nextScheduledAt) {
            // Thêm timeOfDay vào nextScheduledAt
            const timeOfDay = campaign.schedule?.timeOfDay || '09:00';
            const [hour, minute] = timeOfDay.split(':').map(Number);
            nextScheduledAt.hour(hour).minute(minute).second(0);
            updateData.nextScheduledAt = nextScheduledAt.toDate();
          }
        }

        await this.broker.call('mktcampaigns.update', {
          id: campaign._id,
          ...updateData
        });

        this.logger.info(`Hoàn thành xử lý campaign ${campaign.name}: ${sentCount} thành công, ${failedCount} thất bại`);

      } catch (error) {
        this.logger.error(`Lỗi xử lý campaign ${campaign.name}:`, error);

        // Đánh dấu campaign thất bại
        await this.broker.call('mktcampaigns.update', {
          id: campaign._id,
          status: 'failed'
        });
      }
    },

    /**
     * Lấy danh sách users từ target groups
     */
    async getUsersFromTargetGroups(targetGroups) {
      const allUsers = [];
      const userIds = new Set();

      for (const group of targetGroups) {
        try {
          const groupUsers = await this.broker.call('mktgroups.getUsers', {id: group._id});
          // Tránh trùng lặp users
          for (const user of groupUsers.docs || groupUsers) {
            if (!userIds.has(user._id.toString())) {
              userIds.add(user._id.toString());
              allUsers.push(user);
            }
          }
        } catch (error) {
          this.logger.error(`Lỗi lấy users từ group ${group._id}:`, error);
        }
      }

      return allUsers;
    },

    /**
     * Gửi email cho một user cụ thể
     */
    async sendEmailToUser(campaign, user) {
      // Tạo tracking ID
      const trackingId = campaign._id
      // Render template với data của user
      const emailContent = await this.broker.call('mkttemplates.preview', {
        id: campaign.templateId._id,
        data: {
          name: user.fullName || user.name || 'Khách hàng',
          email: user.email,
          account: user.account || user.email,
          support_email: "<EMAIL>",
          company_name: "Công ty Cổ Phần Công Nghệ Clickee",
        }
      });

      // Tạo log
      const log = await this.broker.call('mktlogs.create', {
        campaignId: campaign._id.toString(),
        userId: user._id.toString(),
        email: user.email,
        subject: emailContent.subject,
        content: emailContent.content,
        status: 'sent',
        trackingId
      });

      // Gửi email với meta internal để bỏ qua permission check
      await this.broker.call('mktsender.send', {
        to: user.email,
        subject: emailContent.subject,
        html: emailContent.content,
        campaignId: campaign._id,
        userId: user._id.toString(),
        trackingId: log.trackingId,
        logId: log._id
      }, {
        meta: {
          internal: true,
          caller: 'mktscheduler'
        }
      });


    },

    /**
     * Cập nhật thống kê campaign
     */
    async updateCampaignStatistics(campaignId, sentCount, failedCount) {
      const campaign = await this.broker.call('mktcampaigns.get', {id: campaignId});
      await this.broker.call('mktcampaigns.update', {
        id: campaignId,
        statistics: {
          ...campaign.statistics,
          totalSent: campaign.statistics.totalSent + sentCount,
          totalFailed: campaign.statistics.totalFailed + failedCount
        }
      });
    },

    /**
     * Tính toán thời gian gửi tiếp theo
     */
    calculateNextScheduleTime(campaign) {
      const {schedule} = campaign;

      if (!schedule || schedule.frequency === 'once') {
        return null; // Chỉ gửi một lần
      }

      const now = moment();
      const timeOfDay = schedule.timeOfDay || '09:00';
      const [hour, minute] = timeOfDay.split(':').map(Number);

      let nextTime;

      switch (schedule.frequency) {
        case 'daily':
          nextTime = moment().add(1, 'day').hour(hour).minute(minute).second(0);
          break;

        case 'weekly':
          nextTime = moment().add(1, 'week').hour(hour).minute(minute).second(0);

          // Nếu có daysOfWeek được chỉ định
          if (schedule.daysOfWeek && schedule.daysOfWeek.length > 0) {
            const currentDay = now.day();
            const nextDay = schedule.daysOfWeek.find(day => day > currentDay) ||
              schedule.daysOfWeek[0];

            if (nextDay > currentDay) {
              nextTime = moment().day(nextDay).hour(hour).minute(minute).second(0);
            } else {
              nextTime = moment().add(1, 'week').day(nextDay).hour(hour).minute(minute).second(0);
            }
          }
          break;

        case 'monthly':
          nextTime = moment().add(1, 'month').hour(hour).minute(minute).second(0);
          break;

        default:
          return null;
      }

      // Kiểm tra endDate
      if (schedule.endDate && nextTime.isAfter(moment(schedule.endDate))) {
        return null;
      }

      return nextTime.toDate();
    }
  },

  events: {
    /**
     * Lắng nghe khi campaign được start
     */
    'email.campaign.started': {
      async handler(ctx) {
        const {campaignId} = ctx.params;

        try {
          const campaign = await this.broker.call('mktcampaigns.get', {id: campaignId});

          if (campaign.type === 'automatic' && campaign.schedule) {
            // Tính toán thời gian gửi đầu tiên
            let firstSchedule;

            if (campaign.schedule.startDate) {
              firstSchedule = new Date(campaign.schedule.startDate);
            } else {
              firstSchedule = this.calculateNextScheduleTime(campaign) || new Date();
            }

            // Cập nhật campaign với nextScheduledAt (isScheduleActive đã được set ở campaign service)
            await this.broker.call('mktcampaigns.update', {
              id: campaignId,
              nextScheduledAt: firstSchedule
            });

            this.logger.info(`Đã schedule campaign ${campaign.name} lần đầu vào ${firstSchedule}`);
          }
        } catch (error) {
          this.logger.error('Lỗi khi schedule campaign:', error);
        }
      }
    }
  }
};
