const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./groups.model');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'mktgroups',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      'createdBy': 'users.get',
    },
  },

  hooks: {},

  actions: {
    /**
     * Create a new email group
     */
    create: {
      params: {
        name: {type: 'string', min: 2},
        description: {type: 'string', optional: true},
        type: {type: 'enum', values: ['automatic', 'manual', 'import'], default: 'manual'},
        automaticType: {
          type: 'enum',
          values: ['never_used_after_signup', 'inactive_over_14_days', 'trial_expired_no_upgrade', 'paid_plan_expiring_soon', 'paid_plan_expired_7_days', 'feedback_upgrade_intent'],
          optional: true
        },
        priority: {type: 'number', optional: true, default: 0},
      },
      async handler(ctx) {
        const {user} = ctx.meta;
        const entity = ctx.params;

        entity.createdBy = user._id;
        entity.userCount = 0;

        // Validate that automatic groups have automaticType
        if (entity.type === 'automatic' && !entity.automaticType) {
          throw new MoleculerClientError('Automatic groups must have an automaticType', 400);
        }

        // Validate that manual groups have conditions
        if (entity.type === 'manual' && (!entity.conditions || Object.keys(entity.conditions).length === 0)) {
          throw new MoleculerClientError('Manual groups must have conditions', 400);
        }
        console.log('entity', entity)
        const doc = await this.adapter.insert(entity);
        return await this.transformDocuments(ctx, {}, doc);
      }
    },

    /**
     * Get users in a group
     */
    getUsers: {
      rest: {
        method: 'GET',
        path: '/:id/users'
      },
      params: {
        id: {type: 'string'},
        page: {type: 'number', optional: true, default: 1},
        pageSize: {type: 'number', optional: true, default: 10},
      },
      async handler(ctx) {
        const {id, page, pageSize} = ctx.params;

        const group = await this.adapter.findById(id);
        if (!group) {
          throw new MoleculerClientError(i18next.t('Group not found'), 404);
        }

        // For automatic groups, query users based on the automaticType
        if (group.type === 'automatic') {
          return this.getGroupUsersAutomatic(group.automaticType);
        } else if (group.type === 'manual') {
          return this.getUsersByConditions(group.conditions, page, pageSize);
        }

        // For manual groups, query users based on the conditions
        return this.broker.call('mktusers.getUsersByGroup', {
          mktGroupId: id
        });
      }
    },

    getUsersByAutomaticType: {
      rest: {
        method: 'GET',
        path: '/getUsersByAutomaticType'
      },
      params: {},
      async handler(ctx) {
        const {automaticType, conditions} = ctx.params;
        if (automaticType) return this.getGroupUsersAutomatic(automaticType);
        return this.getUsersByConditions(conditions);
      }
    }
  },

  methods: {


    /**
     * Get users by automatic type
     */
    async getGroupUsersAutomatic(automaticType) {
      console.log('automaticType', automaticType)
      switch (automaticType) {
        case 'never_used_after_signup': // Người dùng đã đăng ký nhưng chưa bao giờ dùng sản phẩm
          return this.getUserNeverUsedAfterSignup();
        case 'inactive_over_14_days': // Người dùng đã dùng lúc đăng ký, nhưng ngưng dùng từ 14 ngày trở lên
          return this.getUserInactiveOver14Days();
        case 'trial_expired_no_upgrade': // Người dùng hết hạn dùng thử mà chưa đăng ký trả phí
          return this.getUserTrialExpiredNoUpgrade();
        case 'paid_plan_expiring_soon': // Người dùng đang trong gói trả phí sắp hết hạn
          return this.getUserPaidPlanExpiringSoon();
        case 'paid_plan_expired_7_days': // Người dùng đã hết hạn gói trả phí mà chưa gia hạn sau 7 ngày
          return this.getUserPaidPlanExpired7Days();
        case 'feedback_upgrade_intent': // Các email có feedback chọn “có nâng cấp”
          return this.getUserFeedbackUpgradeIntent();
      }
      return [];
    },

    /**
     * Get users by manual conditions
     */
    async getUsersByConditions(conditions, page, pageSize) {
      try {

        if (!conditions) return [];
        // Nếu có điêù kiện registrationDateRange, tìm người dùng theo thời gian dăng ký
        if (conditions.registrationDateRange?.from && conditions.registrationDateRange?.to) {
          return this.getUserByRegistrationDateRange(conditions.registrationDateRange);
        }

        if (conditions.paidRegistrationDateRange?.from && conditions.paidRegistrationDateRange?.to) {
          return this.getUserByPaidRegistrationDateRange(conditions.paidRegistrationDateRange);
        }

        if (conditions.packageExpiryDateRange?.from && conditions.packageExpiryDateRange?.to) {
          return this.getUserByPackageExpiryDateRange(conditions.packageExpiryDateRange);
        }
        if (conditions.feedbackStatus) {
          return this.getUserByFeedbackStatus(conditions.feedbackStatus);
        }
        if (conditions.totalUsageDays?.min && conditions.totalUsageDays?.max) {
          return this.getUserByTotalUsageDays(conditions.totalUsageDays);
        }
        if (conditions.daysNotLoggedIn?.min && conditions.daysNotLoggedIn?.max) {
          return this.getUserByDaysNotLoggedIn(conditions.daysNotLoggedIn);
        }
        // if (conditions.packageType) {
        //   return this.getUserByPackageType(conditions.packageType);
        // }
        if (conditions.usedPromotionCode) {
          return this.getUserByUsedPromotionCode(conditions.usedPromotionCode);
        }
        return [];
      } catch (error) {
        this.logger.error(`Error getting users by conditions:`, error);
        return {rows: [], total: 0, page, pageSize};
      }
    },

    // get user registered but never used the product
    async getUserNeverUsedAfterSignup() {
      try {
        //get tracking from 1970 to now
        const tracking = await this.broker.call('trackings.getTracking', {
          fromDate: Math.floor((Date.UTC(2023, 0, 1, 0, 0, 0, 0) - Date.UTC(1970, 0, 1, 0, 0, 0, 0)) / 1000), //2023 in timestamp
          toDate: Math.floor(Date.now() / 1000), //now in timestamp
          time: 'custom',
          page: 1,
          limit: 10000
        });
        return tracking.users.rows.filter(item => item.numberOfSubmits === 0 && item.numberOfProjects === 0);
      } catch (error) {
        this.logger.error(`Error getting inactive users:`, error);
        return []
      }
    },
    async getUserInactiveOver14Days() {
      try {
        const tracking = await this.broker.call('trackings.getTracking', {
          fromDate: Math.floor((Date.now() - 14 * 24 * 60 * 60 * 1000) / 1000), //15 days ago in timestamp
          toDate: Math.floor(Date.now() / 1000), //now in timestamp
          time: 'custom',
          page: 1,
          limit: 10000
        });
        return tracking.users.rows.filter(item => item.visitNumber === 0);
      } catch (error) {
        this.logger.error(`Error getting inactive users:`, error);
        return []
      }
    },
    async getUserTrialExpiredNoUpgrade() {
      try {

        const userIds = await this.broker.call('subscriptions.getUserTrialExpiredNoUpgrade');
        return this.broker.call('users.find', {query: {_id: {$in: userIds}}});

      } catch (error) {
        this.logger.error(`Error getting users:`, error);
        return []
      }
    },
    async getUserPaidPlanExpiringSoon() {
      try {
        // Lấy tất cả users có type = 'student' (vì chỉ student mới có checkExpiringSubscription)
        const students = await this.broker.call('users.find', {
          query: {
            type: 'student',
            isDeleted: false,
            active: true
          }
        });
        if (!students || students.length === 0) {
          return [];
        }

        const usersWithExpiringSoon = [];

        // Kiểm tra từng student để xem có subscription sắp hết hạn không
        for (const user of students) {
          try {
            // Sử dụng cùng logic như API me
            const [subscriptions, permission] = await Promise.all([
              this.broker.call("subscriptions.checkExpiringSubscription", {userId: user._id}),
              this.broker.call("permissions.checkQuota", {userId: user._id})
            ]);

            // Nếu checkExpiringSubscription trả về true (có subscription sắp hết hạn)
            // và checkQuota trả về true (quota thấp - sắp hết hạn)
            if (subscriptions && permission) {
              usersWithExpiringSoon.push(user);
            }
          } catch (error) {
            // Log lỗi nhưng tiếp tục với user tiếp theo
            this.logger.warn(`Error checking expiring subscription for user ${user._id}:`, error.message);
          }
        }
        this.logger.info(`Found ${usersWithExpiringSoon.length} users with paid plan expiring soon`);
        return usersWithExpiringSoon;

      } catch (error) {
        this.logger.error(`Error getting users with paid plan expiring soon:`, error);
        return [];
      }
    },

    async getUserPaidPlanExpired7Days() {
      try {
        const currentDate = new Date();
        const sevenDaysAgo = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);

        // Lấy tất cả users có type = 'student' hoặc 'teacher' (có thể có paid subscriptions)
        const users = await this.broker.call('users.find', {
          query: {
            type: {$in: ['student', 'teacher']},
            isDeleted: false,
            active: true
          }
        });

        if (!users || users.length === 0) {
          return [];
        }

        const usersWithExpiredPaidPlans = [];

        // Kiểm tra từng user
        for (const user of users) {
          try {
            // Lấy customer của user
            const customer = await this.broker.call("customers.getOneByUser", {userId: user._id});

            if (!customer) {
              continue; // Skip nếu không có customer
            }

            // Lấy tất cả paid subscriptions của customer (bao gồm cả ACTIVE và INACTIVE)
            const paidSubscriptions = await this.broker.call("subscriptions.find", {
              query: {
                customerId: customer._id,
                isFree: false // Chỉ lấy gói trả phí
              }
            });

            if (!paidSubscriptions || paidSubscriptions.length === 0) {
              continue; // Skip nếu user chưa từng có gói trả phí
            }

            // Kiểm tra xem tất cả gói trả phí đã hết hạn quá 7 ngày chưa
            let allPaidPlansExpiredOver7Days = true;
            let hasAnyPaidPlan = false;

            for (const subscription of paidSubscriptions) {
              hasAnyPaidPlan = true;

              // Nếu subscription vẫn còn ACTIVE hoặc chưa hết hạn quá 7 ngày
              if (subscription.status === 'ACTIVE' || subscription.endDate > sevenDaysAgo) {
                allPaidPlansExpiredOver7Days = false;
                break; // Có 1 gói không thỏa mãn điều kiện thì user không đủ điều kiện
              }
            }

            // Chỉ thêm user nếu:
            // 1. Có ít nhất 1 gói trả phí
            // 2. Tất cả gói trả phí đều đã hết hạn quá 7 ngày
            if (hasAnyPaidPlan && allPaidPlansExpiredOver7Days) {
              usersWithExpiredPaidPlans.push(user);
            }

          } catch (error) {
            // Log lỗi nhưng tiếp tục với user tiếp theo
            this.logger.warn(`Error checking expired paid plans for user ${user._id}:`, error.message);
          }
        }
        console.log("usersWithExpiredPaidPlans", usersWithExpiredPaidPlans)
        this.logger.info(`Found ${usersWithExpiredPaidPlans.length} users with all paid plans expired over 7 days`);
        return usersWithExpiredPaidPlans;

      } catch (error) {
        this.logger.error(`Error getting users with paid plan expired 7 days:`, error);
        return [];
      }
    },

    async getUserFeedbackUpgradeIntent() {
      try {
        // Tìm tất cả feedback có upgrade intent = true (bool: true)
        const upgradeFeedbacks = await this.broker.call("userFeedbacks.find", {
          query: {
            bool: true, // User muốn nâng cấp
            isDeleted: false
          },
          populate: ["submitFeedbackId"]
        });

        if (!upgradeFeedbacks || upgradeFeedbacks.length === 0) {
          return [];
        }

        // Lấy danh sách userIds từ submitFeedbacks và chuyển thành string để loại bỏ trùng lặp
        const userIdStrings = [...new Set(
          upgradeFeedbacks
            .map(feedback => feedback.submitFeedbackId?.userId?.toString())
            .filter(userId => userId) // Loại bỏ null/undefined
        )];

        if (userIdStrings.length === 0) {
          return [];
        }
        const usersWithUpgradeIntent = [];

        // Kiểm tra từng user xem đã có paid subscription chưa
        for (const userIdString of userIdStrings) {
          try {
            // Lấy thông tin user
            const user = await this.broker.call("users.get", {id: userIdString});

            if (!user || user.isDeleted || !user.active) {
              continue; // Skip user không hợp lệ
            }

            // Lấy customer của user
            const customer = await this.broker.call("customers.getOneByUser", {userId: user._id});

            if (!customer) {
              continue; // Skip nếu không có customer
            }

            // Kiểm tra xem user đã có paid subscription chưa
            const paidSubscriptions = await this.broker.call("subscriptions.find", {
              query: {
                customerId: customer._id,
                isFree: false // Chỉ lấy gói trả phí
              }
            });

            // Chỉ thêm user nếu chưa có gói trả phí nào
            if (!paidSubscriptions || paidSubscriptions.length === 0) {
              usersWithUpgradeIntent.push(user);
            }

          } catch (error) {
            // Log lỗi nhưng tiếp tục với user tiếp theo
            this.logger.warn(`Error checking upgrade intent for user ${userIdString}:`, error.message);
          }
        }
        this.logger.info(`Found ${usersWithUpgradeIntent.length} users with feedback upgrade intent and no paid plans`);
        return usersWithUpgradeIntent;

      } catch (error) {
        this.logger.error(`Error getting users with feedback upgrade intent:`, error);
        return [];
      }
    },

    async getUserByRegistrationDateRange(registrationDateRange) {
      try {
        const createdAt = {
          $gte: registrationDateRange.from,
          $lte: registrationDateRange.to
        }

        return await this.broker.call("users.find", {
          query: {
            createdAt
          }
        });
      } catch (error) {
        this.logger.error(`Error getting users with feedback upgrade intent:`, error);
        return [];
      }
    },
    async getUserByPaidRegistrationDateRange(paidRegistrationDateRange) {
      try {
        const createdAt = {
          $gte: paidRegistrationDateRange.from,
          $lte: paidRegistrationDateRange.to
        }

        const subscriptions = await this.broker.call("subscriptions.find", {
          query: {
            createdAt,
            isFree: false,
          }
        });
        const customers = subscriptions.map(subscription => subscription.customerId);
        return [...new Set(customers)];
      } catch (error) {
        this.logger.error(`Error getting users with feedback upgrade intent:`, error);
        return [];
      }
    },
    async getUserByPackageExpiryDateRange(packageExpiryDateRange) {
      try {
        const endDate = {
          $gte: packageExpiryDateRange.from,
          $lte: packageExpiryDateRange.to
        }

        const subscriptions = await this.broker.call("subscriptions.find", {
          query: {
            endDate,
            isFree: false,
          }
        });
        const customers = subscriptions.map(subscription => subscription.customerId);
        return [...new Set(customers)];
      } catch (error) {
        this.logger.error(`Error getting users with feedback upgrade intent:`, error);
        return [];
      }
    },
    async getUserByFeedbackStatus(feedbackStatus) {
      try {
        // Tìm tất cả feedback có upgrade intent = true (bool: true)
        const upgradeFeedbacks = await this.broker.call("userFeedbacks.find", {
          query: {
            bool: feedbackStatus === 'upgrade',
            isDeleted: false
          },
          populate: ["submitFeedbackId"]
        });

        if (!upgradeFeedbacks || upgradeFeedbacks.length === 0) {
          return [];
        }

        // Lấy danh sách userIds từ submitFeedbacks và chuyển thành string để loại bỏ trùng lặp
        const userIdStrings = [...new Set(
          upgradeFeedbacks
            .map(feedback => feedback.submitFeedbackId?.userId?.toString())
            .filter(userId => userId) // Loại bỏ null/undefined
        )];

        if (userIdStrings.length === 0) {
          return [];
        }
        const usersWithUpgradeIntent = [];

        // Kiểm tra từng user xem đã có paid subscription chưa
        for (const userIdString of userIdStrings) {
          try {
            // Lấy thông tin user
            const user = await this.broker.call("users.get", {id: userIdString});

            if (!user || user.isDeleted || !user.active) {
              continue; // Skip user không hợp lệ
            }

            // Lấy customer của user
            const customer = await this.broker.call("customers.getOneByUser", {userId: user._id});

            if (!customer) {
              continue; // Skip nếu không có customer
            }

            // Kiểm tra xem user đã có paid subscription chưa
            const paidSubscriptions = await this.broker.call("subscriptions.find", {
              query: {
                customerId: customer._id,
                isFree: false // Chỉ lấy gói trả phí
              }
            });

            // Chỉ thêm user nếu chưa có gói trả phí nào
            if (!paidSubscriptions || paidSubscriptions.length === 0) {
              usersWithUpgradeIntent.push(user);
            }

          } catch (error) {
            // Log lỗi nhưng tiếp tục với user tiếp theo
            this.logger.warn(`Error checking upgrade intent for user ${userIdString}:`, error.message);
          }
        }
        this.logger.info(`Found ${usersWithUpgradeIntent.length} users with feedback upgrade intent and no paid plans`);
        return usersWithUpgradeIntent;

      } catch (error) {
        this.logger.error(`Error getting users with feedback upgrade intent:`, error);
        return [];
      }
    },
    async getUserByTotalUsageDays(totalUsageDays) {
      try {
        const users = await this.broker.call("users.find", {
          query: {
            totalUsageDays: {
              $gte: totalUsageDays.min,
              $lte: totalUsageDays.max
            }
          }
        });
        return users;
      } catch (error) {
        this.logger.error(`Error getting users with feedback upgrade intent:`, error);
        return [];
      }
    },
    async getUserByDaysNotLoggedIn(daysNotLoggedIn) {
      try {
        const users = await this.broker.call("users.find", {
          query: {
            daysNotLoggedIn: {
              $gte: daysNotLoggedIn.min,
              $lte: daysNotLoggedIn.max
            }
          }
        });
        return users;
      } catch (error) {
        this.logger.error(`Error getting users with feedback upgrade intent:`, error);
        return [];
      }
    },
    async getUserByPackageType(packageId) {
      try {
        const subscriptions = await this.broker.call("subscriptions.find", {
          query: {
            packageId: {
              $in: packageId
            }
          }
        });
        const customers = subscriptions.map(subscription => subscription.customerId);
        // lọc trùng theo email của customers
        return customers.filter(customer => !!customer)
          .filter((customer, index, self) =>
              index === self.findIndex((t) => (
                t.email === customer?.email
              ))
          );
      } catch (error) {
        this.logger.error(`Error getting users with feedback upgrade intent:`, error);
        return [];
      }
    },
    async getUserByUsedPromotionCode(usedPromotionCode) {
      try {
        console.log("usedPromotionCode", usedPromotionCode)
        const transactions = await this.broker.call("transactions.find", {
          query: {
            promotionId: {
              $in: usedPromotionCode
            },
            state: "done"
          }
        });
        return transactions;
      } catch (error) {
        this.logger.error(`Error getting users with feedback upgrade intent:`, error);
        return [];
      }
    }

  },

  events: {}
}
