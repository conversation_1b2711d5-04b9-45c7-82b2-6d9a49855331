const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const moment = require('moment');

module.exports = {
  name: 'mktstatistics',
  mixins: [FunctionsCommon],

  settings: {},

  actions: {
    /**
     * L<PERSON>y thống kê email marketing
     */
    getStatistics: {
      rest: {
        method: 'GET',
        path: '/campaigns'
      },
      async handler(ctx) {
        try {
          const {campaign, time, fromDate, toDate} = ctx.params;
          // Validate custom date range
          if (time === 'custom') {
            if (!fromDate || !toDate) {
              throw new MoleculerClientError('fromDate và toDate là bắt buộc khi time=custom', 400);
            }
            if (fromDate > toDate) {
              throw new MoleculerClientError('fromDate không thể lớn hơn toDate', 400);
            }
          }

          // Tính toán date range
          const dateRange = this.extractQueryTime({time, fromDate, toDate});
          // Validate campaign exists nếu không phải 'all'
          if (campaign !== 'all') {
            try {
              await this.broker.call('mktcampaigns.get', {id: campaign});
            } catch (error) {
              throw new MoleculerClientError('Campaign không tồn tại', 404);
            }
          }

          console.log("dateRange", dateRange)
          // Lấy dữ liệu logs theo filter
          const logs = await this.getFilteredLogs(campaign, dateRange);

          // Lấy danh sách campaigns để tính performance
          const campaigns = await this.getFilteredCampaigns(campaign, dateRange);

          // Tính toán các metrics
          const overview = this.calculateOverviewMetrics(logs);
          const campaignPerformance = this.calculateCampaignPerformance(campaigns, logs);
          const timeSeriesData = this.calculateTimeSeriesData(logs, dateRange);
          const deviceData = this.generateDeviceData(); // Mock data
          const recentActivity = this.getRecentActivity(logs);

          return {
            overview,
            campaignPerformance,
            timeSeriesData,
            deviceData,
            recentActivity
          };

        } catch (error) {
          this.logger.error('Lỗi khi lấy thống kê:', error);
          throw error;
        }
      }
    }
  },

  methods: {
    /**
     * Lấy logs đã được filter
     */
    async getFilteredLogs(campaign, dateRange) {
      const query = {...dateRange};

      // Filter theo campaign nếu không phải 'all'
      if (campaign !== 'all') {
        query.campaignId = campaign;
      }
      return this.broker.call('mktlogs.find', {
        query,
        populate: ['campaignId']
      });

    },

    /**
     * Lấy campaigns đã được filter
     */
    async getFilteredCampaigns(campaign, dateRange) {
      const query = {};

      // Filter theo campaign nếu không phải 'all'
      if (campaign !== 'all') {
        query._id = campaign;
      }

      return await this.broker.call('mktcampaigns.find', {
        query,
        populate: ['templateId']
      });

    },

    /**
     * Tính toán overview metrics
     */
    calculateOverviewMetrics(logs) {
      // Nhóm logs theo trackingId để tránh đếm trùng
      const logsByTracking = this.groupLogsByTracking(logs);

      const totalSent = logsByTracking.filter(log =>
        ['sent', 'opened', 'clicked', 'converted'].includes(log.status)
      ).length;

      const totalOpened = logsByTracking.filter(log =>
        ['opened', 'clicked', 'converted'].includes(log.status)
      ).length;

      const totalClicked = logsByTracking.filter(log =>
        ['clicked', 'converted'].includes(log.status)
      ).length;

      const totalUnsubscribed = 0; // Chưa có tracking unsubscribe

      const openRate = totalSent > 0 ? (totalOpened / totalSent * 100) : 0;
      const clickRate = totalSent > 0 ? (totalClicked / totalSent * 100) : 0;
      const unsubscribeRate = 0; // Chưa có tracking unsubscribe

      return {
        totalSent,
        totalOpened,
        totalClicked,
        totalUnsubscribed,
        openRate: Math.round(openRate * 10) / 10,
        clickRate: Math.round(clickRate * 10) / 10,
        unsubscribeRate: Math.round(unsubscribeRate * 10) / 10
      };
    },

    /**
     * Nhóm logs theo trackingId để lấy trạng thái cao nhất
     */
    groupLogsByTracking(logs) {
      const trackingMap = new Map();
      const statusPriority = {
        'queued': 0,
        'sent': 1,
        'opened': 2,
        'clicked': 3,
        'converted': 4,
        'failed': -1
      };

      logs.forEach(log => {
        const trackingId = log.trackingId || `${log.campaignId}_${log.email}`;
        const existing = trackingMap.get(trackingId);

        if (!existing || statusPriority[log.status] > statusPriority[existing.status]) {
          trackingMap.set(trackingId, log);
        }
      });

      return Array.from(trackingMap.values());
    },

    /**
     * Tính toán campaign performance
     */
    calculateCampaignPerformance(campaigns, logs) {
      return campaigns.map(campaign => {
        const campaignLogs = logs.filter(log =>
          log.campaignId && log.campaignId._id.toString() === campaign._id.toString()
        );

        // Nhóm logs theo trackingId để tránh đếm trùng
        const groupedLogs = this.groupLogsByTracking(campaignLogs);

        const sent = groupedLogs.filter(log =>
          ['sent', 'opened', 'clicked', 'converted'].includes(log.status)
        ).length;

        const opened = groupedLogs.filter(log =>
          ['opened', 'clicked', 'converted'].includes(log.status)
        ).length;

        const clicked = groupedLogs.filter(log =>
          ['clicked', 'converted'].includes(log.status)
        ).length;

        const unsubscribed = 0; // Chưa có tracking unsubscribe

        const openRate = sent > 0 ? (opened / sent * 100) : 0;
        const clickRate = sent > 0 ? (clicked / sent * 100) : 0;
        const unsubscribeRate = 0;

        return {
          id: campaign._id.toString(),
          name: campaign.name,
          sent,
          opened,
          clicked,
          unsubscribed,
          openRate: Math.round(openRate * 10) / 10,
          clickRate: Math.round(clickRate * 10) / 10,
          unsubscribeRate: Math.round(unsubscribeRate * 10) / 10
        };
      });
    },

    /**
     * Tính toán time series data
     */
    calculateTimeSeriesData(logs, dateRange) {
      const timeSeriesMap = new Map();
      // Tạo map với tất cả ngày trong range
      const current = moment(dateRange.createdAt["$gte"]);
      const end = moment(dateRange.createdAt["$lte"]);

      while (current.isSameOrBefore(end, 'day')) {
        const dateKey = current.format('YYYY-MM-DD');
        timeSeriesMap.set(dateKey, {
          date: dateKey,
          sent: 0,
          opened: 0,
          clicked: 0
        });
        current.add(1, 'day');
      }

      // Nhóm logs theo trackingId trước khi đếm
      const groupedLogs = this.groupLogsByTracking(logs);

      // Đếm logs theo ngày
      groupedLogs.forEach(log => {
        const dateKey = moment(log.createdAt).format('YYYY-MM-DD');
        const dayData = timeSeriesMap.get(dateKey);

        if (dayData) {
          if (['sent', 'opened', 'clicked', 'converted'].includes(log.status)) {
            dayData.sent++;
          }
          if (['opened', 'clicked', 'converted'].includes(log.status)) {
            dayData.opened++;
          }
          if (['clicked', 'converted'].includes(log.status)) {
            dayData.clicked++;
          }
        }
      });

      return Array.from(timeSeriesMap.values());
    },

    /**
     * Generate device data (mock data vì chưa có tracking device)
     */
    generateDeviceData() {
      return [
        {
          type: 'Desktop',
          value: 45.2
        },
        {
          type: 'Mobile',
          value: 38.7
        },
        {
          type: 'Tablet',
          value: 16.1
        }
      ];
    },

    /**
     * Lấy recent activity
     */
    getRecentActivity(logs) {
      // Lấy 100 activities gần nhất với các action quan trọng
      const recentLogs = logs
        .filter(log => ['opened', 'clicked', 'converted'].includes(log.status))
        .sort((a, b) => {
          // Sắp xếp theo thời gian action (openedAt, clickedAt, convertedAt) hoặc updatedAt
          const timeA = a.openedAt || a.clickedAt || a.convertedAt || a.updatedAt;
          const timeB = b.openedAt || b.clickedAt || b.convertedAt || b.updatedAt;
          return new Date(timeB) - new Date(timeA);
        })
        .slice(0, 100);

      return recentLogs.map(log => {
        // Lấy timestamp chính xác theo action
        let timestamp = log.updatedAt;
        if (log.status === 'opened' && log.openedAt) timestamp = log.openedAt;
        if (log.status === 'clicked' && log.clickedAt) timestamp = log.clickedAt;
        if (log.status === 'converted' && log.convertedAt) timestamp = log.convertedAt;

        return {
          id: log._id.toString(),
          campaign: log.campaignId ? log.campaignId.name : 'Unknown Campaign',
          email: this.maskEmail(log.email), // Mask email để bảo mật
          action: log.status,
          timestamp: timestamp
        };
      });
    },

    /**
     * Mask email để bảo mật
     */
    maskEmail(email) {
      if (!email) return 'Unknown';
      const [username, domain] = email.split('@');
      if (!domain) return email;

      const maskedUsername = username.length > 2
        ? username.substring(0, 2) + '*'.repeat(username.length - 2)
        : username;

      return `${maskedUsername}@${domain}`;
    }
  }
};
