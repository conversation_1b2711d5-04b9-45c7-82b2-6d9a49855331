"use strict";

const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const Model = require("./exercises.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const axios = require('axios');
const path = require("path");
const fs = require("fs");
const storageDir = path.join(__dirname, "storage");
const sdk = require("microsoft-cognitiveservices-speech-sdk");
const i18next = require("i18next");
const {INPUT_TYPE} = require("../../../constants/constant");
const {MoleculerClientError} = require("moleculer").Errors;

module.exports = {
  name: "exercises",
  mixins: [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Model), FunctionsCommon, BaseService, FileMixin, AuthRole],

  settings: {
    entityValidator: {},
    populates: {
      "audioId": 'files.get',
      "avatarId": 'files.get',
      "createdBy": 'users.get',
      "updatedBy": 'users.get',
    },
    populateOptions: ["audioId", "createdBy", "updatedBy", "avatarId"],
  },

  hooks: {
    after: {
      "createExercise|updateExercise|deleteExercise": function (ctx, res) {
        const exerciseId = res._id;
        const performedBy = ctx.meta.user?._id;
        const action = ctx.action.name.includes("create") ? "create" : ctx.action.name.includes("update") ? "update" : "delete";
        ctx.call("exerciselogs.createLog", {action, exerciseId, performedBy});
        return res;
      },
    },
  },
  dependencies: [],

  actions: {
    upload: {
      async handler(ctx) {
        ctx.meta.$multipart.folder = "dictation_shadowing";
        ctx.meta.$multipart.fileType = "audio";
        const audioData = await ctx.call("files.upload", ctx.params, {meta: ctx.meta});
        const filePath = await ctx.call("files.filePath", {id: audioData._id});

        const {text, segments, duration} = await this.broker.call("whisper.segmentTranscript", {
          audioPath: filePath,
        });

        if (!text) throw new MoleculerClientError("Cant get text from this audio", 400);

        if (segments) {
          // Process segments in batches of 20 to avoid overwhelming the API
          const batchSize = 20;
          for (let i = 0; i < segments.length; i += batchSize) {
            const batch = segments.slice(i, i + batchSize);
            await Promise.all(batch.map(async (segment) => {
              segment.hiddenWord = await this.broker.call("exercises.autoChoseHiddenWord", {
                sentence: segment.text,
              });
            }));
          }
        }

        return {
          audioId: audioData._id,
          text,
          duration,
          segments: segments.map(({start, end, text, hiddenWord}) => {
            return {start, end, text, hiddenWord}
          })
        };
      }
    },

    createAudioFromText: {
      rest: "POST /textToSpeech",
      async handler(ctx) {
        const {text, voice, speed} = ctx.params;


        const audio = await this.broker.call("tts.textToSpeech", {text, voice, speed});
        const buffer = Buffer.from(await audio.arrayBuffer());
        const file = await ctx.call("files.createFromAudioBuffer", {buffer, folder: "dictation_shadowing"});

        const filePath = await ctx.call("files.filePath", {id: file._id});
        const {segments, duration} = await this.broker.call("whisper.segmentTranscript", {
          audioPath: filePath,
        });

        // Process segments in batches
        if (segments) {
          const batchSize = 20;
          for (let i = 0; i < segments.length; i += batchSize) {
            const batch = segments.slice(i, i + batchSize);
            await Promise.all(batch.map(async (segment) => {
              segment.hiddenWord = await this.broker.call("exercises.autoChoseHiddenWord", {
                sentence: segment.text,
              });
            }));
          }
        }

        return {
          audioId: file._id,
          text,
          duration,
          segments: segments.map(({start, end, text, hiddenWord}) => {
            return {start, end, text, hiddenWord}
          })
        };
      }
    },

    createExercise: {
      rest: "POST /createExercise",
      async handler(ctx) {
        const {name, difficulty, type, timeLimit, audioId, transcript, segments, tag, avatarId, status} = ctx.params;
        const user = ctx.meta.user;
        if (!user || !user.isSystemAdmin) {
          throw new MoleculerClientError(i18next.t("error_permission_denied"), 403);
        }
        return this.adapter.insert({
          name,
          status,
          tag,
          difficulty,
          type,
          segments,
          timeLimit: type !== "shadowing" ? timeLimit : null,
          audioId,
          transcript,
          avatarId,
          createdBy: user._id,
          updatedBy: user._id,
        });
      },
    },
    // Cập nhật bài tập
    updateExercise: {
      rest: "PUT /:id/updateExercise",
      async handler(ctx) {
        const {id, ...updateData} = ctx.params;
        const user = ctx.meta.user;

        if (!user?.isSystemAdmin) {
          throw new Error("Permission denied");
        }

        const exercise = await this.adapter.findById(id);
        if (!exercise) {
          throw new MoleculerClientError(i18next.t("error_exercise_not_found"), 404);
        }

        const shouldUpdateHistory = updateData.audioId && updateData.audioId !== exercise.audioId;
        if (shouldUpdateHistory) {
          exercise.versionHistory.push(this.createVersionHistoryEntry(exercise, user._id));
          if (exercise.versionHistory.length > 3) {
            exercise.versionHistory.shift();
          }
        }

        Object.assign(exercise, updateData, {updatedBy: user._id});
        await exercise.save();

        return exercise;
      }


    },

    // Xóa bài tập (xóa mềm)
    deleteExercise: {
      rest: "DELETE /:id/deleteExercise",
      async handler(ctx) {
        const {id} = ctx.params;
        const user = ctx.meta.user; // Thông tin user từ middleware

        if (!user || !user.isSystemAdmin) {
          throw new MoleculerClientError(i18next.t("error_permission_denied"), 403);
        }

        const exercise = await this.adapter.findById(id);
        if (!exercise) throw new MoleculerClientError(i18next.t("error_exercise_not_found"), 403);

        exercise.deletedAt = new Date();
        exercise.isDeleted = true;
        await exercise.save();
        return exercise
      },
    },

    getAllExerciseSegments: {
      rest: "GET /:exerciseId/details",
      async handler(ctx) {
        const {exerciseId, query} = ctx.params;
        const parsedQuery = JSON.parse(query || '{}');
        const mode = parsedQuery.mode;
        const exerciseType = parsedQuery.exerciseType;

        // Get exercise data
        const exercise = await this.adapter.findById(exerciseId);
        if (!exercise) throw new MoleculerClientError(i18next.t("error_exercise_not_found"), 404);
        const exerciseTransformed = await this.transformDocuments(ctx, {}, exercise);

        // Build query for submissions
        const submissionQuery = {
          exerciseId,
          studentId: ctx.meta.user._id,
        };

        // Add exerciseType for shadowing exercises
        if (exerciseType === "shadowing") {
          submissionQuery.exerciseType = exerciseType;
        }

        if (exerciseType === "dictation") {
          submissionQuery.exerciseType = exerciseType;
        }

        if (mode === "word" || mode === "sentence") {
          submissionQuery.mode = mode;
        }

        console.log(submissionQuery);
        // Fetch all submissions for this exercise and student
        const submissions = await this.broker.call("exercisesubmissions.find", {
          query: submissionQuery
        });

        console.log(submissions, submissions.length);
        const getKey = (segment, exerciseType, mode) => {
          return `${segment.text?.toString()}_${segment.start?.toString()}_${segment.end?.toString()}_${exerciseType}_${mode}`;
        }
        // Create a map to look up submissions by segment ID and mode
        const submissionMap = new Map();
        submissions.forEach(submission => {
          const key = getKey(submission.segment, submission.exerciseType, submission.mode);
          submissionMap.set(key, submission);
        });

        // Process segments
        const details = exercise.segments.map(segment => {
          // Determine the mode for this segment
          let segmentMode = mode;

          if (mode === 'mixed') {
            // In mixed mode, randomly assign 'word' or 'sentence' to each segment
            segmentMode = Math.random() < 0.5 ? 'word' : 'sentence';
          }

          // Look up the submission for this segment and mode
          const submissionKey = getKey(segment, exerciseType, segmentMode);
          const submission = submissionMap.get(submissionKey);
          console.log(submissionKey, submission);

          return {
            segment: segment,
            mode: segmentMode,
            ...(submission || {})
          };
        });

        const {name, tag, difficulty, type, audioId, avatarId, _id} = exerciseTransformed;
        return {
          _id,
          name,
          tag,
          difficulty,
          type,
          audioId,
          avatarId,
          exercises: details
        };
      },
    },

    getAll: {
      rest: "GET /allPublished",
      async handler(ctx) {
        try {
          const {query: queryString = "{}", sort, populate} = ctx.params;
          const query = {...JSON.parse(queryString), status: "published"};
          const params = this.constructParams(ctx.params, query, sort);

          const res = await ctx.call("exercises.list", params);
          res.rows.forEach(item => {
            item.numberExercise = item.segments.length;
            delete item.segments;
          });

          return res;
        } catch (error) {
          console.error('Error in handler:', error);
          throw error; // Let the caller deal with this mess
        }
      }


    },

    autoChoseHiddenWord: {
      rest: {
        method: "POST",
        path: "/autoChoseHiddenWord",
      },
      async handler(ctx) {
        const {sentence} = ctx.params;

        const prompt = `Choose one content word (noun, verb, adjective, or adverb) from this English sentence to hide. Return only the word, no explanation: "${sentence}"`;

        const messages = [
          {
            role: "system",
            content: "You are a language expert. Choose one content word from the given sentence. Return only the word."
          },
          {role: "user", content: prompt},
        ];

        try {
          return this.broker.call("tools.submitFastLLM", {
            messages,
            temperature: 0.3,
            max_tokens: 10,
          });
        } catch (error) {
          console.error("Error in autoChoseHiddenWord:", error.message);
          throw error;
        }
      }
    }


  },

  events: {},

  methods: {
    constructParams(params, query, sort) {
      return {
        ...this.extractParamsList(params),
        searchFields: "tag,name",
        query: JSON.stringify(query),
        fields: "name tag difficulty type audioId avatarId _id segments",
        sort,
        populate: [],
      };
    },
    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },
    createVersionHistoryEntry(exercise, userId) {
      const {name, tag, difficulty, type, segments, timeLimit, transcript, status, audioId} = exercise;
      return {name, tag, difficulty, type, segments, timeLimit, transcript, status, audioId, updatedBy: userId};
    }
  },

  created() {
  },

  async started() {
    this.createFolderIfNotExist(storageDir);
  },

  async stopped() {
  },
};
