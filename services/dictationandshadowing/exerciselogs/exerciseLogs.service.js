"use strict";

const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const Model = require("./exerciseLogs.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const axios = require('axios');
const path = require("path");
const fs = require("fs");
const storageDir = path.join(__dirname, "storage");
const sdk = require("microsoft-cognitiveservices-speech-sdk");
const i18next = require("i18next");
const {getAudioDurationInSeconds} = require("get-audio-duration");
const {MoleculerClientError} = require("moleculer").Errors;

module.exports = {
  name: "exerciselogs",
  mixins: [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Model), FunctionsCommon, BaseService, FileMixin, AuthRole],

  settings: {
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },

  dependencies: [],

  actions: {
    createLog: {
      rest: 'POST /createLog',
      async handler(ctx) {
        const {exerciseId, action, performedBy} = ctx.params;
        return this.adapter.insert({exerciseId, action, performedBy});
      }
    }
  },

  events: {},

  methods: {},

  created() {
  },

  async started() {
  },

  async stopped() {
  },
};
