const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {EXERCISES, EXERCISE_LOGS, USER} = require("../../../constants/dbCollections");

const schema = new Schema({
    exerciseId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: EXERCISES, // Liên kết với bài tập
      required: true
    },
    action: {
      type: String,
      enum: ['create', 'update', 'delete', 'hide', 'publish'], // Hành động
      required: true
    },
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    details: String,
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);
module.exports = mongoose.model(EXERCISE_LOGS, schema, EXERCISE_LOGS);
