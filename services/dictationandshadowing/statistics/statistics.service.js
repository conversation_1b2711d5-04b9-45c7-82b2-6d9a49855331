"use strict";

const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const Model = require("./statistics.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");

module.exports = {
  name: "exercisestatistics",
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService, FileMixin, AuthRole],

  settings: {
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },

  dependencies: [],

  actions: {
  },

  events: {},

  methods: {},

  created() {
  },

  async started() {
  },

  async stopped() {
  },
};
