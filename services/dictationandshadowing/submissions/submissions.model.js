const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {EXERCISES, EXERCISE_SUBMISSIONS, USER, FILE} = require("../../../constants/dbCollections");

const schema = new Schema({
    exerciseId: {type: mongoose.Schema.Types.ObjectId, ref: EXERCISES, required: true},
    studentId: {type: mongoose.Schema.Types.ObjectId, ref: USER, required: true},
    studentAudioId: {type: mongoose.Schema.Types.ObjectId, ref: FILE},
    exerciseType: {
      type: String,
      enum: ['dictation', 'shadowing'],
      required: true
    },
    segment: {type: Schema.Types.Mixed},
    mode: {
      type: String,
      enum: ['word', 'sentence'],
    },
    result: {type: String, enum: ['correct', 'incorrect']},
    correctAnswer: {type: Schema.Types.Mixed},
    studentAnswer: {type: Schema.Types.Mixed},
    accuracyScore: {type: Number},
    premiumResults: {type: Schema.Types.Mixed},
    standardResults: {type: Schema.Types.Mixed},
    spentTime: {type: Number},
    exerciseVersion: {type: Number},
    errorLists: [
      {
        word: String,
        correctWord: String
      },
    ],
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);
module.exports = mongoose.model(EXERCISE_SUBMISSIONS, schema, EXERCISE_SUBMISSIONS);
