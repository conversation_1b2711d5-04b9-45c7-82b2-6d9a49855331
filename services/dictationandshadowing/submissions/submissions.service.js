"use strict";

const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const Model = require("./submissions.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const i18next = require("i18next");
const {INPUT_TYPE} = require("../../../constants/constant");
const {MoleculerClientError} = require("moleculer").Errors;

module.exports = {
  name: "exercisesubmissions",
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService, FileMixin, AuthRole],

  settings: {
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },

  hooks: {
    before: {
      // "checkAnswerDictation|shadowingSubmission": "checkSubmissionPermission",
    }
  },
  dependencies: [],

  actions: {
    checkAnswerDictation: {
      rest: "POST /checkAnswerDictation",
      async handler(ctx) {
        const {segment, studentAnswer, exerciseId, mode, exerciseType = "dictation", submissionId} = ctx.params;
        if (!segment || !studentAnswer || !exerciseId) {
          throw new MoleculerClientError("Missing required parameters", 400, "MISSING_PARAMS", ["segment", "studentAnswer", "exerciseId"]);
        }
        const userId = ctx.meta.user?._id;

        const insertData = {
          segment,
          studentAnswer,
          exerciseId,
          studentId: userId,
          mode,
          exerciseType,
          errorLists: [],
          correctAnswer: mode === "word" ? segment.hiddenWord : segment.text
        };
        this.evaluateDictationAnswer(insertData, mode, segment, studentAnswer);
        ctx.emit("dictationShadowingSubmitted", {type: "DICTATION"});
        if (submissionId) {
          const existingSubmission = await this.adapter.findById(submissionId);
          if (!existingSubmission) {
            throw new MoleculerClientError("Submission not found", 400);
          }
          return this.adapter.updateById(existingSubmission._id, insertData, {new: true});
        } else {
          return this.adapter.insert(insertData);
        }
      }
    },


    resetAnswerDictation: {
      rest: "POST /resetAnswerDictation",
      async handler(ctx) {
        const {submissionId} = ctx.params;
        const deletedData = await this.adapter.removeById(submissionId);
        if (!deletedData) {
          throw new MoleculerClientError("Submission not found", 400);
        }
        return {
          segment: deletedData?.segment,
          mode: deletedData?.mode
        }
      }
    },

    shadowingSubmission: {
      rest: "POST /shadowingSubmission",
      async handler(ctx) {
        const {
          accuracyScore,
          segment,
          studentAudioId,
          studentId,
          exerciseId,
          exerciseType = "shadowing",
          studentAnswer,
          standardResults,
          premiumResults
        } = ctx.params;
        const userId = ctx.meta.user?._id;

        const existingSubmission = await this.findExistingSubmission(userId, exerciseId, segment._id, null, exerciseType);

        const insertData = {
          segment,
          accuracyScore,
          exerciseId,
          studentId,
          studentAudioId,
          exerciseType,
          studentAnswer,
          standardResults,
          premiumResults
        };
        ctx.emit("dictationShadowingSubmitted", {type: "SHADOWING"});
        return existingSubmission
          ? this.adapter.updateById(existingSubmission._id, insertData)
          : this.adapter.insert(insertData);
      }
    },

    calculateShadowingCost: {
      rest: "GET /calculateShadowingCost",
      async handler(ctx) {
        try {
          const {query} = ctx.params;
          query.exerciseType = "shadowing";
          // Tạo query để lọc theo thời gian và exerciseType
          query.exerciseType = "shadowing"

          // Lấy danh sách các bài nộp shadowing
          const submissions = await this.adapter.find({query});

          // Lấy thông tin về giá của model gpt-4o-mini-transcribe
          const modelInfo = await this.broker.call("gptmodelprice.findOne", {gptModel: "gpt-4o-mini-transcribe"});

          if (!modelInfo) {
            throw new MoleculerClientError("Model price information not found", 400);
          }

          // tính toán chi phi
          const submissionCosts = await Promise.all(submissions.map(submission => this.calculateCostForShadowing(submission)));
          const totalCost = submissionCosts.reduce((acc, cost) => acc + cost, 0);

          return {
            numberSubmit: submissions.length,
            totalCost,
            toolName: "Shadowing"
          }
        } catch (error) {
          this.logger.error("Error calculating shadowing cost:", error);
          throw new MoleculerClientError("Error calculating shadowing cost", 500);
        }
      }
    },

    getStudentSubmissions: {
      rest: "GET /getStudentSubmissions",
      async handler(ctx) {
        const {studentId, exerciseId, exerciseType} = ctx.params;
        return this.adapter.find({query: {studentId, exerciseId, exerciseType}});
      }
    },

    statisticDictationShadowingTracking: {
      async handler(ctx) {
        try {
          const {query} = ctx.params;

          // Aggregate submissions by studentId and exerciseType using Model.aggregate
          const results = await Model.aggregate([
            {$match: query},
            {
              $group: {
                _id: {
                  studentId: "$studentId",
                  exerciseType: "$exerciseType"
                },
                numberSubmit: {$sum: 1}
              }
            },
            {
              $group: {
                _id: "$_id.studentId",
                dictationSubmits: {
                  $sum: {
                    $cond: [
                      {$eq: ["$_id.exerciseType", "dictation"]},
                      "$numberSubmit",
                      0
                    ]
                  }
                },
                shadowingSubmits: {
                  $sum: {
                    $cond: [
                      {$eq: ["$_id.exerciseType", "shadowing"]},
                      "$numberSubmit",
                      0
                    ]
                  }
                }
              }
            },
            {
              $project: {
                _id: {$toString: "$_id"},
                dictationSubmits: 1,
                shadowingSubmits: 1,
                totalSubmits: {$add: ["$dictationSubmits", "$shadowingSubmits"]}
              }
            }
          ]);

          // Calculate costs for shadowing submissions
          const shadowingQuery = {...query, exerciseType: "shadowing"};
          const shadowingSubmissions = await this.adapter.find({query: shadowingQuery});

          // Group shadowing submissions by studentId and calculate costs
          const shadowingCostMap = {};
          for (const submission of shadowingSubmissions) {
            const studentId = submission.studentId.toString();
            if (!shadowingCostMap[studentId]) {
              shadowingCostMap[studentId] = 0;
            }
            const cost = await this.calculateCostForShadowing(submission);
            shadowingCostMap[studentId] += cost;
          }

          // Merge cost data with submission counts
          const finalResults = results.map(result => ({
            ...result,
            shadowingTotalCost: shadowingCostMap[result._id] || 0,
            dictationTotalCost: 0 // Dictation doesn't have cost calculation
          }));

          return finalResults;
        } catch (error) {
          this.logger.error("Error in statisticDictationShadowingTracking:", error);
          throw new MoleculerClientError("Error calculating dictation/shadowing statistics", 500);
        }
      }
    }
  },

  events: {},

  methods: {
    async checkSubmissionPermission(ctx) {
      try {
        const actionName = ctx.action.rawName;
        console.log("actionName", actionName)
        const userId = ctx.meta.user?._id;

        if (!userId) {
          throw new MoleculerClientError(i18next.t("user_not_authenticated"), 401, "UNAUTHORIZED");
        }
        // Lấy tất cả quyền của người dùng
        const permissions = await ctx.call("permissions.find", {query: {userId}});

        if (!permissions || permissions.length === 0) {
          this.logger.warn(`No permissions found for user ${userId}`);
          throw new MoleculerClientError(i18next.t("no_subscription_found"), 403, "FORBIDDEN");
        }

        // Lọc ra những quyền đang active
        const activePermissions = permissions.filter(item =>
          item.subscriptionId && item.subscriptionId.status === "ACTIVE"
        );

        if (activePermissions.length === 0) {
          this.logger.warn(`No active permissions found for user ${userId}`);
          throw new MoleculerClientError(i18next.t("no_active_subscription"), 403, "FORBIDDEN");
        }

        const isDictation = actionName === "checkAnswerDictation";
        const usedKey = isDictation ? "dictationUsed" : "shadowingUsed";
        const limitKey = isDictation ? "dictationLimit" : "shadowingLimit";

        // Tìm quyền có còn lượt sử dụng không
        const haveAccess = activePermissions.find(item => {
          const limit = Number(item.accessLimit[limitKey] || 0);
          const used = Number(item.accessLimit[usedKey] || 0);
          const startDate = item.subscriptionId && item.subscriptionId.startDate
            ? new Date(item.subscriptionId.startDate)
            : null;

          return limit > used && startDate && startDate <= new Date();
        });

        if (!haveAccess) {
          const actionType = isDictation ? "dictation" : "shadowing";
          this.logger.warn(`User ${userId} has reached their ${actionType} submission limit`);

          const msg = isDictation ? "submit_dictation_limited" : "submit_shadowing_limited";
          throw new MoleculerClientError(i18next.t(msg), 403, "LIMIT");
        }

      } catch (error) {
        if (error instanceof MoleculerClientError) {
          throw error;
        }
        this.logger.error("Error in checkStudentSubmitPermission:", error);
        throw new MoleculerClientError(i18next.t("permission_check_error"), 500, "INTERNAL_ERROR");
      }
    },
    async calculateCostForShadowing(submission, modelPrice = 0.003) {
      const audioDuration = await this.broker.call("files.getAudioDuration", {id: submission.studentAudioId});
      return Number(audioDuration) * modelPrice / 60 || 0;
    },

    async findExistingSubmission(userId, exerciseId, segmentId, mode, exerciseType) {
      const query = {
        studentId: userId,
        exerciseId,
        exerciseType
      };

      if (mode) {
        query.mode = mode;
      }

      const submissions = await this.adapter.find({query});
      return submissions.find(item => item.segment._id.toString() === segmentId.toString());
    },

    evaluateDictationAnswer(insertData, mode, segment, studentAnswer) {
      // Loại bỏ dấu câu trong câu trả lời gồm các dấu , . ! ?

      const normalizeText = text => text.replace(/[.,!?]/g, ' ').toLowerCase().split(" ").filter(Boolean);
      const normalizeWord = text => text.replace(/[.,!?]/g, ' ').toLowerCase().trim();

      if (mode === "word") {
        const correctWords = normalizeWord(segment.hiddenWord);
        const answerWords = normalizeWord(studentAnswer);
        if (correctWords !== answerWords) {
          insertData.errorLists.push({word: segment.hiddenWord, correctWord: studentAnswer});
          insertData.result = "incorrect";
        } else {
          insertData.result = "correct";
        }
      } else if (mode === "sentence") {
        const correctWords = normalizeText(segment.text);
        const answerWords = normalizeText(studentAnswer);
        let isCorrect = true;

        correctWords.forEach((correctWord, index) => {
          const answerWord = answerWords[index] || "";
          if (correctWord !== answerWord) {
            isCorrect = false;
            insertData.errorLists.push({word: answerWord, correctWord});
          }
        });

        insertData.result = isCorrect ? "correct" : "incorrect";
      }
    }
  },

  created() {
  },

  async started() {
  },

  async stopped() {
  },
};
