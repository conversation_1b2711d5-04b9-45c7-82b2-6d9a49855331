"use strict";

const fs = require("fs");
const path = require("path");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const FileMixin = require("../../mixins/file.mixin");
const AudioModel = require("./audio.model");
const DbMongoose = require("../../mixins/dbMongo.mixin");
const storageDir = path.join(__dirname, "storage");
const {getAudioDurationInSeconds} = require('get-audio-duration');
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
const {USER_CODES} = require("../../constants/constant");

module.exports = {
  name: "audios",
  mixins: [DbMongoose(AudioModel), FunctionsCommon, FileMixin],

  /**
   * Settings
   */
  settings: {
    populates: {
      "audioFileId": "files.get",
    },
    populateOptions: ["audioFileId"]
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    /**
     * Youtube video detail
     *
     * @returns
     */
    create: {
      role: USER_CODES.SYSTEM_ADMIN
    },
    update: {
      role: USER_CODES.SYSTEM_ADMIN
    },
    list: {
      role: USER_CODES.SYSTEM_ADMIN
    },
    get: {
      role: USER_CODES.SYSTEM_ADMIN
    },
    remove: {
      role: USER_CODES.SYSTEM_ADMIN
    },
    audioTranscript: {
      timeout: 2 * 60 * 1000,
      rest: {
        method: "GET",
        path: "/audio-transcript",
      },
      // visibility: "protected",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {audioId, cutStart, cutEnd} = ctx.params;
          return this.getAudioTranscript(audioId, cutStart, cutEnd);
        } catch (e) {
          throw new MoleculerClientError("Something went wrong!", 400);
        }
      },
    },
    audioDetail: {
      timeout: 2 * 60 * 1000,
      // visibility: "protected",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {audioId} = ctx.params;
          return this.getAudioDetail(audioId);
        } catch (e) {
          throw new MoleculerClientError("Something went wrong!", 400);
        }
      },
    },
    clearAudio: {
      async handler() {
        this.clearFilesInFolder(storageDir);
      }
    },
    getOne: {
      rest: {
        method: "GET",
        path: "/getOne",
      },
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {audioFileId} = ctx.params;

          const dirPath = this.getDirPath('audio', storageDir);
          const [file, contentAudio] = await Promise.all([
            this.broker.call('files.get', {id: audioFileId}),
            this.broker.call('files.stream', {id: audioFileId}),
          ]);

          const localFilePath = this.getFilePath(file.name, dirPath);

          if (!fs.existsSync(localFilePath)) {
            await this.saveToLocalStorage(contentAudio, localFilePath);
          }
          const duration = await getAudioDurationInSeconds(localFilePath);
          const audio = await this.adapter.findOne({audioFileId});
          return await this.adapter.updateById(audio._id, {duration: duration});
        } catch (e) {
          throw new MoleculerClientError("Something went wrong!", 400);
        }
      },
    },

    downloadCutAudio: {
      rest: {
        method: "GET",
        path: "/downloadCutAudio",
      },
      async handler(ctx) {
        try {
          const {audioId, cutStart, cutEnd} = ctx.params;
          const audio = await this.adapter.findOne({audioFileId: audioId});
          const audioTransform = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, audio);
          const contentAudio = await this.broker.call('files.stream', {id: audioId});

          const dirPath = this.getDirPath('audio', storageDir);
          const extension = path.extname(audioTransform.audioFileId.name);
          const fileName = path.basename(audioTransform.audioFileId.name, extension);
          const localFilePath = this.getFilePath(audioTransform.audioFileId.name, dirPath);
          const mp3FilePath = this.getFilePath(`${fileName}_from_${cutStart}_to_${cutEnd}${extension}`, dirPath);

          if (!fs.existsSync(localFilePath)) {
            await this.saveToLocalStorage(contentAudio, localFilePath);
          }

          if (!fs.existsSync(mp3FilePath)) {
            await this.cuttingVideoAudio(localFilePath, mp3FilePath, cutStart, cutEnd);
          }

          ctx.meta.$responseHeaders = {
            "Content-Disposition": `attachment;filename=${encodeURI(audioTransform.audioFileId.name)}`
          };
          return fs.createReadStream(mp3FilePath, {});
        } catch (e) {
          throw new MoleculerClientError("Something went wrong!", 400);
        }
      },
    },

    textToSpeech: {
      rest: {
        method: "POST",
        path: "/textToSpeech",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {text, voice, speed} = ctx.params;
          console.log("text, voice", text, voice, speed);
          const dirPath = this.getDirPath('audio', storageDir);
          const filePath = this.getFilePath(`audio_${Date.now()}.mp3`, dirPath);

          const audio = await this.broker.call("tts.textToSpeech", {text, voice, speed});
          const buffer = Buffer.from(await audio.arrayBuffer());
          await fs.promises.writeFile(filePath, buffer);
          const file = await ctx.call("files.createFromAudioBuffer", {buffer});
          const duration = await getAudioDurationInSeconds(filePath);
          const audioObject = {
            audioFileId: file._id,
            audioName: `audio_${Date.now()}.mp3`,
            displayName: `audio_${Date.now()}.mp3`,
            duration
          };
          return await this.adapter.insert(audioObject);
        } catch (e) {
          throw new MoleculerClientError(i18next.t("something_went_wrong"), 400);
        }
      }
    }
  },

  /**
   * Events
   */
  events: {

    resourceAudioDeleted: {
      async handler(ctx) {
        try {
          const {id} = ctx.params;
          const audio = await this.adapter.removeById(id);
          ctx.call("files.remove", {id: audio.audioFileId});

          return audio;
        } catch (e) {
          console.log(e);
        }
      }
    }
  },

  /**
   * Methods
   */
  methods: {
    async getAudioById(audioId) {
      return await this.adapter.findOne({audioFileId: audioId});
    },

    async getAudioDetail(audioId) {
      try {
        const cachedAudio = await this.getAudioById(audioId);
        if (cachedAudio) return cachedAudio;
        return await this.adapter.insert({
          audioFileId: audioId
        });
      } catch (error) {
        console.log("getAudioDetail========================================================", error);
      }
    },

    async getTranscriptByWhispering(audioId, cutStart, cutEnd) {
      try {
        const dirPath = this.getDirPath('audio', storageDir);
        const [file, contentAudio] = await Promise.all([
          this.broker.call('files.get', {id: audioId}),
          this.broker.call('files.stream', {id: audioId}),
        ]);
        const localFilePath = this.getFilePath(file.name, dirPath);
        if (!fs.existsSync(localFilePath)) {
          await this.saveToLocalStorage(contentAudio, localFilePath);
        }
        const extension = path.extname(file.name);
        const fileName = path.basename(file.name, extension);

        const cuttingPromises = [];
        for (let i = cutStart; i <= cutEnd; i += 60) {
          const start = i;
          const end = i + 60 < cutEnd ? i + 60 : cutEnd;
          const outputFilePath = this.getFilePath(`${fileName}_from_${start}_to_${end}${extension}`, dirPath);
          cuttingPromises.push(this.cuttingVideoAudio(localFilePath, outputFilePath, start, end));
        }
        const results = await Promise.all(cuttingPromises);

        const promises = results.map(result => {
          return this.broker.call("whisper.transcriptAudio", {
            audioPath: result,
          });
        });

        const transcripts = await Promise.all(promises);
        console.log("transcripts", transcripts)
        // Delete mp3 files
        results.forEach(result => {
          fs.unlinkSync(result);
        });
        return transcripts.map(transcript => transcript.text).join(" ");
      } catch (error) {
        console.log(error);
        return "";
      }
    },
    getAudioTranscriptFromCache(audioDetail, cutStart = 0, cutEnd = 0) {
      if (audioDetail.transcripts && audioDetail.transcripts.length > 0) {
        const transcript = audioDetail.transcripts.find(
          (t) => t.cutStart == cutStart && t.cutEnd == cutEnd,
        );
        if (transcript) {
          return transcript.text;
        }
      }
    },
    async cacheAudioTranscript(audioDetail, cutStart = 0, cutEnd = 0, text) {
      audioDetail.transcripts.push({
        cutStart,
        cutEnd,
        text: text,
      });
      await this.adapter.updateById(audioDetail._id, audioDetail);
    },
    async getAudioTranscript(audioId, cutStart = 0, cutEnd = 0) {
      const audioDetail = await this.getAudioDetail(audioId);
      const cachedTranscript = this.getAudioTranscriptFromCache(
        audioDetail,
        cutStart,
        cutEnd,
      );
      if (cachedTranscript) {
        return cachedTranscript;
      }
      const transcriptFromWhisper = await this.getTranscriptByWhispering(audioId, cutStart, cutEnd);
      if (transcriptFromWhisper && transcriptFromWhisper !== "") {
        await this.cacheAudioTranscript(
          audioDetail,
          cutStart,
          cutEnd,
          transcriptFromWhisper,
        );
        return transcriptFromWhisper;
      }
      return "";
    },
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
