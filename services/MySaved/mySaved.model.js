const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { PROJECT, FOLDER, USER, MY_SAVED } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  userId: { type: Schema.Types.ObjectId, required: true, ref: USER },
  projectId: { type: Schema.Types.ObjectId, ref: PROJECT },
  folderId: { type: Schema.Types.ObjectId, ref: FOLDER },
  isDeleted: { type: Boolean, default: false },

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.index({ userId: 1, projectId: 1, folderId: 1 });
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(MY_SAVED, schema, MY_SAVED);

