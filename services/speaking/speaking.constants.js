"use strict";

/**
 * Constants for IELTS Speaking service
 */

// Enum for speaking parts
const SPEAKING_PARTS = {
  PART1: "part1",
  PART2: "part2",
  PART3: "part3",
  FULL_TEST: "full_test",
};

// Session status
const SESSION_STATUS = {
  COMPLETED: "completed",
  IN_PROGRESS: "in_progress",
  ERROR: "error"
};

// Scoring ranges
const SCORE_RANGE = {
  MIN: 0,
  MAX: 9
};

// Default durations (in seconds)
const DEFAULT_DURATIONS = {
  PART1_ANSWER: 30,
  PART2_PREP: 60,
  PART2_ANSWER: 120,
  PART3_ANSWER: 40
};

// Number of questions per part
const QUESTIONS_COUNT = {
  PART1: 5,
  PART2: 1,
  PART3: 3
};

module.exports = {
  SPEAKING_PARTS,
  SESSION_STATUS,
  SCORE_RANGE,
  DEFAULT_DURATIONS,
  QUESTIONS_COUNT
};
