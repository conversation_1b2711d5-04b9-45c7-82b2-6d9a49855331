"use strict";

const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const Model = require("./spkExercises.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const path = require("path");
const fs = require("fs");
const storageDir = path.join(__dirname, "storage");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;

module.exports = {
  name: "spkexercises",
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService, FileMixin],

  settings: {
    entityValidator: {},
    populates: {
      "avatarId": 'files.get',
      "createdBy": 'users.get',
      "updatedBy": 'users.get',
    },
    populateOptions: ["createdBy", "updatedBy", "avatarId"],
    // Enable caching for better performance
    cache: {
      enabled: true,
      ttl: 60 * 10 // 10 minutes cache for exercises
    }
  },
  dependencies: [],

  actions: {

    createHint: {
      rest: "POST /createHint",
      async handler(ctx) {
        try {
          const {text: question} = ctx.params;

          if (!question) {
            throw new MoleculerClientError("Question text is required", 400);
          }
          const explainInstructions = await this.broker.call("explain.getExplainByType", {
            explainType: "hints",
            taskType: "speaking_room"
          })
          // Tạo prompt để yêu cầu AI tạo hint cho câu hỏi
          const messages = [
            {
              role: "system",
              content: `${explainInstructions.instruction}`
            },
            {
              role: "user",
              content: `The input question: ${question}`
            }
          ];
          // Gọi API tools.submitFastLLM để nhận câu trả lời từ AI
          const hint = await this.broker.call("tools.submitFastLLM", {
            messages,
            temperature: 0.7,
            max_tokens: 200
          });

          return {text: hint};
        } catch (error) {
          this.logger.error("Error creating hint:", error);
          throw error;
        }
      }
    },

    createAudioFromText: {
      rest: "POST /textToSpeech",
      async handler(ctx) {
        const {text, voice, speed} = ctx.params;


        const audio = await this.broker.call("tts.textToSpeech", {text, voice, speed});
        const buffer = Buffer.from(await audio.arrayBuffer());
        const file = await ctx.call("files.createFromAudioBuffer", {buffer, folder: "dictation_shadowing"});

        const filePath = await ctx.call("files.filePath", {id: file._id});
        const transcript = await this.broker.call("whisper.transcriptAudio", {
          audioPath: filePath,
        });

        return {
          audioId: file._id,
          text: transcript.text,
        };
      }
    },

    createExercise: {
      rest: "POST /createExercise",
      async handler(ctx) {
        const {title, topic, status, parts, avatarId} = ctx.params;
        const user = ctx.meta.user;
        if (!user || !user.isSystemAdmin) {
          throw new MoleculerClientError(i18next.t("error_permission_denied"), 403);
        }
        return this.adapter.insert({title, topic, status, parts, avatarId, createdBy: user._id, updatedBy: user._id});
      },
    },
    // Cập nhật bài tập
    updateExercise: {
      rest: "PUT /:id/updateExercise",
      async handler(ctx) {

        const {id, ...updateData} = ctx.params;
        const user = ctx.meta.user;

        if (!user?.isSystemAdmin) {
          throw new Error("Permission denied");
        }

        const exercise = await this.adapter.findById(id);
        if (!exercise) {
          throw new MoleculerClientError(i18next.t("error_exercise_not_found"), 404);
        }

        Object.assign(exercise, updateData, {updatedBy: user._id});
        await exercise.save();

        return exercise;
      }


    },

    // Xóa bài tập (xóa mềm)
    deleteExercise: {
      rest: "DELETE /:id/deleteExercise",
      async handler(ctx) {

        const {id} = ctx.params;
        const user = ctx.meta.user;

        if (!user?.isSystemAdmin) {
          throw new Error("Permission denied");
        }

        const exercise = await this.adapter.findById(id);
        if (!exercise) {
          throw new MoleculerClientError(i18next.t("error_exercise_not_found"), 404);
        }

        exercise.isDeleted = true;
        await exercise.save();
        return exercise

      },
    },
    getAll: {
      rest: "GET /allPublished",
      cache: {
        keys: ["#userID", "query", "sort", "page", "pageSize"],
        ttl: 60 * 5 // 5 minutes cache per user
      },
      async handler(ctx) {
        try {
          const {query: queryString = "{}", sort, populate} = ctx.params;
          const query = {...JSON.parse(queryString), status: "published"};
          const params = this.constructParamsOptimized(ctx.params, query, sort);

          // Get exercises with optimized field selection
          const res = await ctx.call("spkexercises.list", params);

          if (!res.rows || res.rows.length === 0) {
            return res;
          }

          // Extract unique topics for session lookup
          const topics = [...new Set(res.rows.map(item => item.topic))];

          // Optimized session completion check using aggregation
          const completedTopics = await this.getCompletedTopicsOptimized(ctx.meta.user._id, topics);

          // Map completion status efficiently
          res.rows.forEach(item => {
            item.isCompleted = completedTopics.has(item.topic);
          });

          return res;
        } catch (error) {
          this.logger.error('Error in getAll handler:', error);
          throw new MoleculerClientError("Failed to fetch exercises", 500, "FETCH_EXERCISES_ERROR");
        }
      }
    },

  },

  events: {},

  methods: {
    constructParams(params, query, sort) {
      return {
        ...this.extractParamsList(params),
        searchFields: "topic",
        query: JSON.stringify(query),
        fields: "title topic avatarId _id parts createdAt updatedAt",
        sort,
        populate: [],
      };
    },

    /**
     * Optimized version of constructParams that excludes parts field for better performance
     */
    constructParamsOptimized(params, query, sort) {
      return {
        ...this.extractParamsList(params),
        searchFields: "topic,title",
        query: JSON.stringify(query),
        fields: "title topic avatarId _id createdAt updatedAt", // Exclude parts field
        sort,
        populate: [],
      };
    },

    /**
     * Optimized method to get completed topics using Set for O(1) lookup
     * @param {String} userId - User ID
     * @param {Array} topics - Array of topics to check
     * @returns {Set} Set of completed topics
     */
    async getCompletedTopicsOptimized(userId, topics) {
      try {
        const sessionCompleted = await this.broker.call("spksessions.find", {
          query: {
            status: "completed",
            userId: userId,
            tag: {$in: topics},
            isDeleted: {$ne: true}
          },
          fields: "tag", // Only fetch tag field for minimal data transfer
          limit: topics.length // Limit results to maximum possible matches
        });

        // Use Set for O(1) lookup performance
        return new Set(sessionCompleted.map(session => session.tag));
      } catch (error) {
        this.logger.error('Failed to get completed topics:', error);

        // Return empty Set on error to prevent breaking the main flow
        return new Set();
      }
    },

    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },
  },

  created() {
  },

  async started() {
    this.createFolderIfNotExist(storageDir);
  },

  async stopped() {
  },
};
