"use strict";

const DbMongoose = require("../../../mixins/dbMongo.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const SessionModel = require("./sessions.model");
const {SPEAKING_PARTS, SESSION_STATUS, QUESTIONS_COUNT} = require("../speaking.constants");
const {MoleculerClientError} = require("moleculer").Errors;
const i18next = require("i18next");
const path = require("path");
const fs = require("fs");
const {ideahub} = require("googleapis/build/src/apis/ideahub");
const {PERMISSION_ACCESS, DOCUMENT_TYPE, USER_CODES} = require("../../../constants/constant");
const storageDir = path.join(__dirname, "../storage");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "spksessions",
  mixins: [DbMongoose(SessionModel), FunctionsCommon, BaseService, FileMixin],

  /**
   * Settings
   */
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    // Enable caching
    // cache: {
    //   enabled: true,
    //   ttl: 60 * 5 // 5 minutes
    // },
    populates: {
      "userId": 'users.get',
    },
    populateOptions: ["userId"],
  },
  hooks: {
    before: {
      "*": "checkPermission",
      "create": "checkSubmissionSpeakingRoom",
    },
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    /**
     * Create a new speaking session
     *
     * @returns {Object} Created session
     */
    create: {
      rest: {
        method: "POST",
        path: "/"
      },
      auth: "required",
      async handler(ctx) {
        try {
          const userId = ctx.meta.userID;

          // Create new session
          const session = await this.adapter.insert({
            userId,
            startTime: new Date(),
            status: SESSION_STATUS.IN_PROGRESS,
            ...ctx.params
          });
          // Create questions for this session - emit event
          await this.broker.emit("sessionCreated", {
            id: session._id.toString(),
            tag: session.tag,
            part: session.part
          });
          return session;
        } catch (error) {
          this.logger.error("Error creating speaking session:", error);
          throw new MoleculerClientError("Failed to create speaking session", 500, "CREATE_SESSION_ERROR");
        }
      }
    },

    /**
     * Complete a speaking session
     *
     * @param {String} id - Session ID
     * @returns {Object} Completed session with results
     */
    complete: {
      rest: {
        method: "PUT",
        path: "/:id/complete"
      },
      auth: "required",
      params: {
        id: {type: "string"}
      },
      permission: PERMISSION_ACCESS.EDITOR,
      async handler(ctx) {
        try {
          const {id, language} = ctx.params;
          const {lang} = ctx.meta
          const session = await this.adapter.findById(id);

          const [answers, overallFeedbackInstruction] = await Promise.all([
            ctx.call("spkanswers.find", {
              query: {
                sessionId: id,
                isDeleted: false
              },
            }),
            this.broker.call("explain.getExplainByType", {explainType: "overall_feedback", taskType: "speaking_room"})
          ]);
          // Calculate scores
          const resultSummary = this.calculateScores(session.part, answers);
          console.log("resultSummary", resultSummary)
          // Get feedback
          const messages = this.createMessages(answers, overallFeedbackInstruction, resultSummary, language || lang);

          const aiResult = await this.broker.call("tools.submitFastLLM", {
            messages,
            temperature: 0.3,
            max_tokens: 1000,
            responseId: id,
            responseFormat: overallFeedbackInstruction.responseFormat
          });
          //update  session
          const html = this.convertMarkDownToHTML(aiResult);
          ctx.emit("speakingRoomCompleted");
          return await this.adapter.updateById(id, {
            endTime: new Date(),
            status: SESSION_STATUS.COMPLETED,
            feedback: html,
            resultSummary
          });

        } catch (error) {
          if (error instanceof MoleculerClientError) throw error;
          const {id} = ctx.params;
          return await this.adapter.updateById(id, {
            endTime: new Date(),
            status: SESSION_STATUS.ERROR,
          });
          this.logger.error("Error completing session:", error);
          throw new MoleculerClientError("Failed to complete session", 500, "COMPLETE_SESSION_ERROR");
        }
      }
    },

    /**
     * Get session details with answers
     *
     * @param {String} id - Session ID
     * @returns {Object} Session with answers
     */
    getDetails: {
      rest: {
        method: "GET",
        path: "/:id/details"
      },
      auth: "required",
      params: {
        id: {type: "string"}
      },
      permission: PERMISSION_ACCESS.VIEWER,
      async handler(ctx) {
        try {
          const {id} = ctx.params;
          const userId = ctx.meta.userID;

          // Get session
          const session = await this.adapter.findById(id);
          if (!session) {
            throw new MoleculerClientError(`Session not found: ${id}`, 404, "SESSION_NOT_FOUND");
          }

          const sessionTransformed = await this.transformDocuments(ctx, {populate: ['userId']}, session)
          // Get answers for this session
          const answers = await ctx.call("spkanswers.find", {
            query: {
              sessionId: id,
              isDeleted: false
            },
          });
          // Get questions for this session
          const questions = await ctx.call("spkquestions.find", {
            query: {
              sessionId: id,
              isDeleted: false
            },
          });
          return {
            ...sessionTransformed,
            answers,
            questions
          };
        } catch (error) {
          if (error instanceof MoleculerClientError) throw error;

          this.logger.error("Error getting session details:", error);
          throw new MoleculerClientError("Failed to get session details", 500, "GET_SESSION_DETAILS_ERROR");
        }
      }
    },

    /**
     * Retrieve all completed speaking sessions for the current user.
     *
     * @param {Object} ctx - Context object containing meta and params
     * @param {Object} ctx.meta - Meta information
     * @param {String} ctx.meta.userID - ID of the current user
     * @returns {Promise<Array>} List of completed sessions
     * @throws {MoleculerClientError} Throws error if retrieval fails
     */
    getAllCompleted: {
      rest: {
        method: "GET",
        path: "/completed"
      },
      auth: "required",
      async handler(ctx) {
        try {
          const {userID: userId} = ctx.meta;
          const {query: queryString = "{}", sort, populate} = ctx.params;
          const parsedQuery = JSON.parse(queryString || "{}");
          const {documentType, tag, ...restQuery} = parsedQuery;
          const query = {...restQuery, status: SESSION_STATUS.COMPLETED};
          if (tag) {
            query.tag = tag.split(",");
          }
          const sharedSessions = await ctx.call("share.find", {
            query: {userId, type: "SESSION"},
            populate: [],
            fields: ["sessionId"]
          });

          const sessionIds = sharedSessions.map(item => item.sessionId);

          if (documentType === DOCUMENT_TYPE.MY_FILE) {
            query.userId = userId;
          } else if (documentType === DOCUMENT_TYPE.SHARED_WITH_ME) {
            query._id = {$in: sessionIds};
          } else {
            query.$or = [{userId}, {_id: {$in: sessionIds}}];
          }

          const params = this.constructParams(ctx.params, query, sort);
          return await ctx.call("spksessions.list", params);

        } catch (error) {
          console.error('Error in handler:', error);
          throw error;
        }
      }
    },

    changeQuestion: {
      rest: {
        method: "PUT",
        path: "/:id/questions"
      },
      auth: "required",
      params: {
        id: {type: "string", min: 1},
        questionId: {type: "string", min: 1},
        part: {type: "string", enum: Object.values(SPEAKING_PARTS)}
      },
      permission: PERMISSION_ACCESS.EDITOR,
      async handler(ctx) {
        try {
          const {id, questionId, part} = ctx.params;
          const userId = ctx.meta.userID;

          // Kiểm tra session tồn tại và quyền truy cập của user
          const session = await this.validateSessionAccess(ctx, id, userId);

          // Lấy dữ liệu exercise với validation
          const exercise = await this.getExerciseForSession(session.tag);

          // Lấy các câu hỏi của part cụ thể với validation
          const selectedQuestions = session.questions.filter((q) => q.part === part);
          if (selectedQuestions.length === 0) {
            throw new MoleculerClientError(`No questions found for part ${part} in this session`, 404, "NO_QUESTIONS_FOUND");
          }

          // Kiểm tra questionId có tồn tại trong các câu hỏi đã chọn
          const targetQuestion = selectedQuestions.find(q => q._id.toString() === questionId);
          if (!targetQuestion) {
            throw new MoleculerClientError(`Question ${questionId} not found in part ${part}`, 404, "QUESTION_NOT_FOUND");
          }

          // Lấy ngân hàng câu hỏi cho part với validation
          const questionBank = this.getQuestionBankForPart(exercise, part);

          // Thay thế câu hỏi
          const result = await this.replaceQuestion(selectedQuestions, questionBank, questionId);

          this.logger.info(`Question ${questionId} replaced successfully for session ${id}, part ${part}`);
          return result;

        } catch (error) {
          if (error instanceof MoleculerClientError) throw error;
          this.logger.error("Error changing question:", error);
          throw new MoleculerClientError("Failed to change question", 500, "CHANGE_QUESTION_ERROR");
        }
      }
    },
    permissionAccess: {
      rest: {
        method: "GET",
        path: "/:id/permission",
      },
      auth: "required",
      async handler(ctx) {
        const {id} = ctx.params;
        const {user} = ctx.meta;

        const session = await this.adapter.findOne({_id: id});

        if (!session) {
          throw new MoleculerClientError(i18next.t("error_project_not_found"), 404);
        }

        const ownerId = session?.userId?.toString();
        const userId = ctx.meta.user?._id.toString();
        // system admin can access all projects
        if (user?.isSystemAdmin) {
          return PERMISSION_ACCESS.OWNER;
        }
        let isLock = false;

        if (ownerId === userId) {
          return PERMISSION_ACCESS.OWNER;
        }

        const [shared, generalAccess] = await Promise.all([
          this.broker.call('share.find', {query: {sessionId: id, isDeleted: false, userId}}),
          this.broker.call('generalAccess.find', {
            query: {sessionId: id, isDeleted: false},
            populate: []
          })
        ]);
        const sharedPermissions = this.checkSharedPermission(shared);
        const generalPermissions = this.checkGeneralAccessPermission(ctx.meta.user, generalAccess);

        if (sharedPermissions === PERMISSION_ACCESS.NO_PERMISSION && [PERMISSION_ACCESS.EDITOR, PERMISSION_ACCESS.VIEWER].includes(generalPermissions)) {
          ctx.call('share.insert', {
            entity: {
              sessionId: id,
              userId,
              permission: generalPermissions,
              type: "SESSION",
              isGeneral: true
            }
          });
        }

        const combinedPermissions = [sharedPermissions, generalPermissions];
        if (combinedPermissions.includes(PERMISSION_ACCESS.EDITOR)) {
          if (isLock) {
            return PERMISSION_ACCESS.VIEWER;
          }
          return PERMISSION_ACCESS.EDITOR;
        }
        if (combinedPermissions.includes(PERMISSION_ACCESS.VIEWER)) {
          return PERMISSION_ACCESS.VIEWER;
        }
        return PERMISSION_ACCESS.NO_PERMISSION;
      }
    },

    remove: {
      rest: "DELETE /:id",
      auth: "required",
      params: {
        id: "string",
      },
      activityLogger: true,
      permission: PERMISSION_ACCESS.VIEWER,
      /** @param {Context} ctx */
      async handler(ctx) {
        const {id} = ctx.params;
        const userId = ctx.meta.user?._id;
        const session = await this.adapter.findOne({_id: id});
        const isOwner = session?.userId?.toString() === userId

        if (!isOwner) {
          await this.broker.call('share.unshare', {sessionId: id, userId});
          return session;
        }

        const dataRes = await this.adapter.updateById(id, {isDeleted: true}, {new: true});
        await ctx.emit('sessions.deleted', id);
        return dataRes;
      }
    },
    speakingAdvanceCost: {
      rest: "GET /speakingAdvanceCost",
      async handler(ctx) {
        try {
          const {query} = ctx.params;

          // Lấy danh sách các bài nộp speaking
          const sessions = await this.adapter.find({query});
          console.log('sessions', sessions.length)

          // Lấy thông tin về giá của model gpt-4o-mini-transcribe
          const modelInfo = await this.broker.call("gptmodelprice.findOne", {gptModel: "gpt-4o-mini"});

          if (!modelInfo) {
            throw new MoleculerClientError("Model price information not found", 400);
          }
          const allAnswers = await this.broker.call("spkanswers.find", {query: {sessionId: {$in: sessions.map(session => session._id)}}});
          // tính toán chi phi
          const answersCost = await Promise.all(allAnswers.map(answer => this.calculateCostForAnswer(answer)));
          const sessionCosts = await Promise.all(sessions.map(session => this.calculateCostForSession(session, modelInfo)));
          return {
            numberSubmit: sessions.length,
            totalCost: answersCost.reduce((total, cost) => total + cost, 0) + sessionCosts.reduce((total, cost) => total + cost, 0),
            toolName: "Speaking Room",
          }
        } catch (error) {
          this.logger.error("Error calculating shadowing cost:", error);
          throw new MoleculerClientError("Error calculating shadowing cost", 500);
        }
      }
    },

  },

  /**
   * Events
   */
  events: {


    llmGenerateCompleted: {
      async handler(context) {
        const entity = context.params;
        const session = await this.adapter.findById(entity.id);
        if (session) {
          const dataUpdate = {
            gptModel: entity.gptModel,
            completionTokens: (session.completionTokens || 0) + entity.completionTokens,
            promptTokens: (session.promptTokens || 0) + entity.promptTokens,
            totalTokens: (session.totalTokens || 0) + entity.totalTokens,
          }
          await this.adapter.updateById(entity.id, dataUpdate);
        }
      },
    },


  },

  /**
   * Methods
   */
  methods: {

    async checkSubmissionSpeakingRoom(ctx) {
      try {
        const userId = ctx.meta.user?._id;

        if (!userId) {
          throw new MoleculerClientError(i18next.t("user_not_authenticated"), 401, "UNAUTHORIZED");
        }
        // Lấy tất cả quyền của người dùng
        const permissions = await ctx.call("permissions.find", {query: {userId}});
        console.log("permissions", permissions)
        if (!permissions || permissions.length === 0) {
          this.logger.warn(`No permissions found for user ${userId}`);
          throw new MoleculerClientError(i18next.t("no_subscription_found"), 403, "FORBIDDEN");
        }

        // Lọc ra những quyền đang active
        const activePermissions = permissions.filter(item =>
          item.subscriptionId && item.subscriptionId.status === "ACTIVE"
        );

        if (activePermissions.length === 0) {
          this.logger.warn(`No active permissions found for user ${userId}`);
          throw new MoleculerClientError(i18next.t("no_active_subscription"), 403, "FORBIDDEN");
        }

        // Tìm quyền có còn lượt sử dụng không
        const haveAccess = activePermissions.find(item => {
          const limit = Number(item.accessLimit.speakingRoomLimit || 0);
          const used = Number(item.accessLimit.speakingRoomUsed || 0);
          const startDate = item.subscriptionId && item.subscriptionId.startDate
            ? new Date(item.subscriptionId.startDate)
            : null;

          return limit > used && startDate && startDate <= new Date();
        });

        if (!haveAccess) {
          this.logger.warn(`User ${userId} has reached their speaking room submission limit`);
          throw new MoleculerClientError(i18next.t("submit_speaking_room_limited"), 403, "LIMIT");
        }

      } catch (error) {
        if (error instanceof MoleculerClientError) {
          throw error;
        }
        this.logger.error("Error in check Submission Speaking Room:", error);
        throw new MoleculerClientError(i18next.t("permission_check_error"), 500, "INTERNAL_ERROR");
      }
    },

    async calculateCostForAnswer(answer, modelPrice = 1.3 / 60) {
      return Number(answer.duration) * modelPrice / 60 || 0;
    },
    async calculateCostForSession(session, modelInfo) {
      const {priceInput, priceOutput} = modelInfo;
      return (Number(session.completionTokens) * priceOutput + Number(session.promptTokens) * priceInput) / 1e6 || 0;
    },

    async checkPermission(context) {
      const {action, params} = context;
      const {id, sessionId} = params;
      console.log(id, sessionId)
      if (action?.permission) {
        const permissionAccess = await context.call('spksessions.permissionAccess', {id: id || sessionId});
        const {permission} = action;

        const isEditAccess = permission === PERMISSION_ACCESS.EDITOR;
        const isViewAccess = permission === PERMISSION_ACCESS.VIEWER;
        const noEditAccess = isEditAccess && !this.editAccess(permissionAccess);
        const noViewAccess = isViewAccess && !this.hasViewAccess(permissionAccess);
        if (noEditAccess || noViewAccess) {
          throw new MoleculerClientError(i18next.t("dont_have_permission_project"), 403, "FORBIDDEN");
        }

        context.params.permission = permissionAccess;
      }
    },
    checkSharedPermission(shared) {
      if (!shared.length) return PERMISSION_ACCESS.NO_PERMISSION;
      if (shared.some(item => item.permission === PERMISSION_ACCESS.EDITOR)) return PERMISSION_ACCESS.EDITOR;
      return PERMISSION_ACCESS.VIEWER;
    },

    checkGeneralAccessPermission(user, generalAccess) {
      const typeAccess = generalAccess[0]?.typeAccess;
      if (typeAccess === "ANYONE_WITH_LINK") {
        return generalAccess[0]?.permission;
      } else {
        return PERMISSION_ACCESS.NO_PERMISSION;
      }
    },
    constructParams(params, query, sort) {
      return {
        ...this.extractParamsList(params),
        searchFields: "name",
        query: JSON.stringify(query),
        sort,
        populate: [],
      };
    },

    /**
     * Kiểm tra quyền truy cập session của user
     * @param {String} sessionId - ID của session
     * @param {String} userId - ID của user
     * @returns {Object} Đối tượng session
     */
    async validateSessionAccess(ctx, sessionId, userId) {
      const session = await ctx.call("spksessions.getDetails", {id: sessionId});
      if (!session) {
        throw new MoleculerClientError(`Session not found: ${sessionId}`, 404, "SESSION_NOT_FOUND");
      }
      // Kiểm tra user có sở hữu session này không
      if (session.userId?._id.toString() !== userId.toString()) {
        throw new MoleculerClientError("Access denied: You don't have permission to modify this session", 403, "ACCESS_DENIED");
      }

      // Kiểm tra session vẫn đang diễn ra (có thể sửa đổi)
      if (session.status === SESSION_STATUS.COMPLETED) {
        throw new MoleculerClientError("Cannot modify questions in a completed session", 400, "SESSION_COMPLETED");
      }

      return session;
    },

    /**
     * Lấy exercise cho session với validation
     * @param {String} tag - Tag/topic của session
     * @returns {Object} Đối tượng exercise
     */
    async getExerciseForSession(tag) {
      const exercise = await this.broker.call("spkexercises.find", {
        query: {
          topic: tag,
          status: "published",
          isDeleted: false
        }
      });

      if (!exercise || exercise.length === 0) {
        throw new MoleculerClientError(`No published exercise found for topic: ${tag}`, 404, "EXERCISE_NOT_FOUND");
      }

      return exercise[0];
    },

    /**
     * Lấy ngân hàng câu hỏi cho part cụ thể với validation
     * @param {Object} exercise - Đối tượng exercise
     * @param {String} part - Part speaking
     * @returns {Array} Mảng ngân hàng câu hỏi
     */
    getQuestionBankForPart(exercise, part) {
      if (!exercise.parts || !Array.isArray(exercise.parts)) {
        throw new MoleculerClientError("Exercise has no parts defined", 400, "INVALID_EXERCISE_STRUCTURE");
      }

      const partData = exercise.parts.find((p) => p.part === part);
      if (!partData) {
        throw new MoleculerClientError(`Part ${part} not found in exercise`, 404, "PART_NOT_FOUND");
      }

      if (!partData.questions || !Array.isArray(partData.questions) || partData.questions.length === 0) {
        throw new MoleculerClientError(`No questions available for part ${part}`, 404, "NO_QUESTIONS_IN_PART");
      }

      return partData.questions;
    },

    /**
     * Thay thế một câu hỏi bằng câu hỏi mới từ ngân hàng câu hỏi
     * @param {Array} selectedQuestions - Các câu hỏi hiện tại đã chọn
     * @param {Array} questionBank - Ngân hàng câu hỏi khả dụng
     * @param {String} targetId - ID của câu hỏi cần thay thế
     * @returns {Object} Câu hỏi đã được cập nhật
     */
    async replaceQuestion(selectedQuestions, questionBank, targetId) {
      // Tìm câu hỏi cần thay thế trong selectedQuestions
      const targetQuestion = selectedQuestions.find(q => q._id.toString() === targetId);
      if (!targetQuestion) {
        throw new MoleculerClientError("Target question not found in selected questions", 404, "TARGET_QUESTION_NOT_FOUND");
      }

      // Tập hợp tất cả audioId và text đã có trong selectedQuestions
      const usedAudioIds = new Set(selectedQuestions.map(q => q.audioId).filter(Boolean));
      const usedTexts = new Set(selectedQuestions.map(q => q.questionText).filter(Boolean));

      // Tìm vị trí câu hỏi trong bank khớp với targetQuestion
      const currentIndex = questionBank.findIndex(q =>
        q.text === targetQuestion.questionText ||
        q.audioId === targetQuestion.audioId
      );

      // Bước 1: Tìm câu hỏi tiếp theo trong bank phù hợp
      let replacement = null;
      for (let i = currentIndex + 1; i < questionBank.length; i++) {
        const q = questionBank[i];
        if (this.isQuestionAvailable(q, usedAudioIds, usedTexts)) {
          replacement = q;
          break;
        }
      }

      // Bước 2: Nếu không tìm thấy, duyệt toàn bộ questionBank từ đầu
      if (!replacement) {
        for (const q of questionBank) {
          if (this.isQuestionAvailable(q, usedAudioIds, usedTexts)) {
            replacement = q;
            break;
          }
        }
      }

      // Nếu vẫn không tìm được
      if (!replacement) {
        throw new MoleculerClientError("No suitable replacement question available", 400, "NO_REPLACEMENT_AVAILABLE");
      }

      // Cập nhật câu hỏi
      const updateData = {
        id: targetId,
        questionText: replacement.text,
        audioId: replacement.audioId,
        hint: replacement.hint
      };

      return await this.broker.call("spkquestions.update", updateData);
    },

    /**
     * Kiểm tra câu hỏi có khả dụng để sử dụng không (chưa được dùng)
     * @param {Object} question - Câu hỏi cần kiểm tra
     * @param {Set} usedAudioIds - Set các audio ID đã sử dụng
     * @param {Set} usedTexts - Set các text câu hỏi đã sử dụng
     * @returns {Boolean} True nếu câu hỏi khả dụng
     */
    isQuestionAvailable(question, usedAudioIds, usedTexts) {
      // Kiểm tra câu hỏi có các field bắt buộc
      if (!question.text) {
        return false;
      }

      // Kiểm tra audioId đã được sử dụng chưa (nếu câu hỏi có audioId)
      if (question.audioId && usedAudioIds.has(question.audioId)) {
        return false;
      }

      // Kiểm tra text đã được sử dụng chưa
      if (usedTexts.has(question.text)) {
        return false;
      }

      return true;
    },

    /**
     * Create folder if not exist
     *
     * @param {String} folderPath - Folder path
     */
    createFolderIfNotExist(folderPath) {
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, {recursive: true});
      }
    },
    createMessages(answers, overallFeedbackInstruction, resultSummary, lang) {
      const messages = [
        {
          role: "system",
          content: `${overallFeedbackInstruction.instruction}`
        }
      ];

      const parts = [SPEAKING_PARTS.PART1, SPEAKING_PARTS.PART2, SPEAKING_PARTS.PART3];
      parts.forEach(part => {
        const partAnswers = answers.filter(answer => answer.part === part);
        if (partAnswers.length > 0) {
          let prompt = `##`;
          partAnswers.forEach((answer, index) => {
            prompt += `Question ${index + 1}: ${answer.questionId.questionText}\nAnswer ${index + 1}: ${answer.transcript}\n\n`;
          });
          messages.push({
            role: "user",
            content: `This is the user's voice transcript and scores for ${part}:\n${prompt}`
          });
        }
      });
      messages.push({
        role: "user",
        content: `This is the user's overall scores:\n${JSON.stringify(resultSummary)}`
      });
      messages.push({
        role: "user",
        content: `Output Language must be in ${lang === "vi" ? "Vietnamese" : "English"}`
      });
      messages.push({
        role: "user",
        content: `Make response as a human teacher not AI`
      });
      return messages;
    },

    calculateScores(part, answers) {
      const calculateAverage = (scores) => {
        if (!scores || scores.length === 0) return 0;
        return scores.reduce((a, b) => a + b, 0) / scores.length;
      };

      const calculateOverall = (scores) => {
        if (!scores || scores.length === 0) return 0;
        const total = scores.reduce((sum, score) => sum + this.calculateIELTSOverall(score.feedback.vocabularyScore, score.feedback.grammarScore, score.feedback.pronScore, score.feedback.fluencyScore), 0);
        return roundToIELTSScore(total / scores.length);
      };

      // Hàm làm tròn điểm về dạng IELTS standard (.0 hoặc .5)
      const roundToIELTSScore = (score) => {
        return Math.round(score * 2) / 2;
      };

      // Nếu không phải full_test, tính điểm cho part cụ thể
      if (part !== undefined && part !== SPEAKING_PARTS.FULL_TEST) {
        const filteredAnswers = answers.filter(answer => answer.part === part);

        if (filteredAnswers.length === 0) {
          return {
            vocabularyScore: 0,
            grammarScore: 0,
            pronScore: 0,
            fluencyScore: 0,
            overall: 0
          };
        }

        const vocabularyScore = roundToIELTSScore(calculateAverage(filteredAnswers.map(answer => answer.feedback.vocabularyScore)));
        const grammarScore = roundToIELTSScore(calculateAverage(filteredAnswers.map(answer => answer.feedback.grammarScore)));
        const pronScore = roundToIELTSScore(calculateAverage(filteredAnswers.map(answer => answer.feedback.pronScore)));
        const fluencyScore = roundToIELTSScore(calculateAverage(filteredAnswers.map(answer => answer.feedback.fluencyScore)));

        return {
          vocabularyScore,
          grammarScore,
          pronScore,
          fluencyScore,
          overall: roundToIELTSScore(this.calculateIELTSOverall(vocabularyScore, grammarScore, pronScore, fluencyScore))
        };
      }

      // Xử lý cho chế độ full_test
      const partTypes = [SPEAKING_PARTS.PART1, SPEAKING_PARTS.PART2, SPEAKING_PARTS.PART3];
      const partScores = {};

      // Tính điểm cho từng part
      partTypes.forEach(partType => {
        const partAnswers = answers.filter(answer => answer.part === partType);

        if (partAnswers.length === 0) {
          // Nếu không làm câu nào trong part này, điểm = 0
          partScores[partType] = {
            vocabulary: 0,
            grammar: 0,
            pronunciation: 0,
            fluency: 0,
            overall: 0
          };
        } else {
          // Tính điểm trung bình của những câu đã làm
          const vocabularyScore = roundToIELTSScore(calculateAverage(partAnswers.map(answer => answer.feedback.vocabularyScore)));
          const grammarScore = roundToIELTSScore(calculateAverage(partAnswers.map(answer => answer.feedback.grammarScore)));
          const pronScore = roundToIELTSScore(calculateAverage(partAnswers.map(answer => answer.feedback.pronScore)));
          const fluencyScore = roundToIELTSScore(calculateAverage(partAnswers.map(answer => answer.feedback.fluencyScore)));
          const overall = calculateOverall(partAnswers);
          partScores[partType] = {
            vocabulary: vocabularyScore,
            grammar: grammarScore,
            pronunciation: pronScore,
            fluency: fluencyScore,
            overall: overall
          };
        }
      });
      // Tính điểm trung bình của tất cả các part (part1 + part2 + part3) / 3
      const avgVocabularyScore = roundToIELTSScore(
        (partScores[SPEAKING_PARTS.PART1].vocabulary +
          partScores[SPEAKING_PARTS.PART2].vocabulary +
          partScores[SPEAKING_PARTS.PART3].vocabulary) / 3
      );

      const avgGrammarScore = roundToIELTSScore(
        (partScores[SPEAKING_PARTS.PART1].grammar +
          partScores[SPEAKING_PARTS.PART2].grammar +
          partScores[SPEAKING_PARTS.PART3].grammar) / 3
      );

      const avgPronScore = roundToIELTSScore(
        (partScores[SPEAKING_PARTS.PART1].pronunciation +
          partScores[SPEAKING_PARTS.PART2].pronunciation +
          partScores[SPEAKING_PARTS.PART3].pronunciation) / 3
      );

      const avgFluencyScore = roundToIELTSScore(
        (partScores[SPEAKING_PARTS.PART1].fluency +
          partScores[SPEAKING_PARTS.PART2].fluency +
          partScores[SPEAKING_PARTS.PART3].fluency) / 3
      );
      const overallScore = roundToIELTSScore(
        (partScores[SPEAKING_PARTS.PART1].overall +
          partScores[SPEAKING_PARTS.PART2].overall +
          partScores[SPEAKING_PARTS.PART3].overall) / 3
      );

      return {
        vocabularyScore: avgVocabularyScore,
        grammarScore: avgGrammarScore,
        pronScore: avgPronScore,
        fluencyScore: avgFluencyScore,
        overall: overallScore
      };
    }

  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    this.createFolderIfNotExist(storageDir);
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  }
};
