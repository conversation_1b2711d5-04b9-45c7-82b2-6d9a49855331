"use strict";

const DbMongoose = require("../../../mixins/dbMongo.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const AnswerModel = require("./answers.model");
const {SPEAKING_PARTS, SCORE_RANGE} = require("../speaking.constants");
const {MoleculerClientError} = require("moleculer").Errors;
const i18next = require("i18next");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "spkanswers",
  mixins: [DbMongoose(AnswerModel), FunctionsCommon, BaseService, FileMixin],

  /**
   * Settings
   */
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    // Enable caching
    cache: {
      enabled: true,
      ttl: 60 * 5 // 5 minutes
    },
    // Populate options
    populates: {
      questionId: {
        action: "spkquestions.get",
      }
    },
    populateOptions: ["questionId"]
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    createByQuestionId: {
      rest: "POST /createByQuestionId",
      auth: "required",
      /** @param {Context} ctx */
      async handler(ctx) {
        const {questionId, ...data} = ctx.params;
        const answer = await this.adapter.findOne({questionId});
        if (!answer) {
          return await ctx.call("spkanswers.insert", {entity: ctx.params});
        }
        return await ctx.call("spkanswers.update", {...data, id: answer._id});

      }
    },

  },

  /**
   * Events
   */
  events: {
    llmGenerateCompleted: {
      async handler(context) {
        const entity = context.params;
        const answer = await this.adapter.findById(entity.id);
        if (answer) {
          await this.adapter.updateById(entity.id, entity);
        }
      },
    },
  },

  /**
   * Methods
   */
  methods: {},

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  }
};
