"use strict";

const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { SPEAKING_PARTS } = require("../speaking.constants");
const { SPEAKING_SESSIONS, SPEAKING_QUESTIONS, SPEAKING_ANSWERS, USER, FILE } = require("../../../constants/dbCollections");

// Schema for speaking answers
const schema = new Schema(
  {
    sessionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: SPEAKING_SESSIONS,
      required: true,
      index: true
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
      required: true,
      index: true
    },
    questionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: SPEAKING_QUESTIONS,
      required: true
    },
    part: {
      type: String,
      enum: Object.values(SPEAKING_PARTS),
      required: true
    },
    audioUrl: {
      type: String
    },
    audioFileId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: FILE
    },
    transcript: {
      type: String
    },
    duration: {
      type: Number
    },
    gptModel: {type: String},
    completionTokens: {type: Number},
    promptTokens: {type: Number},
    totalTokens: {type: Number},
    audioDuration: {type: Number},
    results: {
      type: Schema.Types.Mixed
    },
    feedback: {
      vocabularyScore: {
        type: Number,
        min: 0,
        max: 9
      },
      grammarScore: {
        type: Number,
        min: 0,
        max: 9
      },
      pronScore: {
        type: Number,
        min: 0,
        max: 9
      },
      fluencyScore: {
        type: Number,
        min: 0,
        max: 9
      },
      comments: String
    },

    isDeleted: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Create indexes
schema.index({ sessionId: 1, part: 1 });
schema.index({ userId: 1, createdAt: -1 });

module.exports = mongoose.model(SPEAKING_ANSWERS, schema, SPEAKING_ANSWERS);
