"use strict";

const WorkspaceModel = require("./workspace.model");
const DbMongoose = require("../../mixins/dbMongo.mixin");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const {USER_SERVICE} = require("../users");
const {ORGANIZATION_SERVICE} = require("../Organization");
const {MoleculerClientError} = require("moleculer").Errors;
const {ObjectId} = require("mongoose").Types;
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");
const i18next = require("i18next");

/**
 *
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "workspaces",
  mixins: [DbMongoose(WorkspaceModel), BaseService, FunctionsCommon, AuthRole],
  /**
   * Settings
   */
  settings: {
    // Validator for the `create` & `insert` actions.
    entityValidator: {},
    populates: {
      "userId": USER_SERVICE.get,
      "organizationId": ORGANIZATION_SERVICE.get,
    },
    populateOptions: ["userId", "organizationId"]
  },

  /**
   * Dependencies
   */
  dependencies: [],

  hooks: {
    before: {
      "update": "checkPermission",
    }
  },

  /**
   * Actions
   */
  actions: {
    list: {
      visibility: 'public'
    },
    update: {
      role: USER_CODES.NORMAL,
    },
    availableWorkspace: {
      rest: {
        method: "GET",
        path: "/available",
      },
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const query = {userId: ctx.meta.user._id, isDeleted: false};

        const organizationUsers = await ctx.call('organizationUsers.find', {query: query, populate: []});
        const organizationIds = this.extractKeyFromList(organizationUsers, 'organizationId');
        return await ctx.call('workspaces.find', {
          query: {organizationId: organizationIds},
          populate: this.settings.populateOptions
        });
      }
    },

    checkAdmin: {
      rest: "GET /:id/checkAdmin",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {id} = ctx.params;
        const {organizationId} = await ctx.call('workspaces.get', {id});
        const admin = await ctx.call('organizationUsers.find', {
          query: {
            userId: ctx.meta.user._id,
            organizationId, isAdmin: true
          }
        });
        if (admin.length === 0) {
          throw new MoleculerClientError("You are not admin of this workspace", 403);
        }
      }
    },

    belongsWorkspace: {
      rest: "GET /:id/belongs",
      auth: "required",
      async handler(ctx) {
        const {id, limit} = ctx.params;
        const notNormalUser = ctx.meta?.user.role !== 'normal';
        const params = {
          query: {workspaceId: id, isDeleted: false, ownerId: ctx.meta.user?._id},
          sort: "-updatedAt",
        };

        const [foldersShareWithMe, projectsShareWithMe] = await Promise.all([
          ctx.call('share.find', {
            query: {type: "FOLDER", userId: ctx.meta.user?._id, isDeleted: false},
            populate: []
          }),
          ctx.call('share.find', {
            query: {type: "PROJECT", userId: ctx.meta.user?._id, isDeleted: false},
            populate: []
          })
        ]);
        const [folders, projects, sharedFolders, sharedProjects, mySaved, orgProjects, orgFolders] = await Promise.all([
          ctx.call('folders.find', params),
          ctx.call('projects.find', {
            query: {workspaceId: id, isDeleted: false, ownerId: ctx.meta.user?._id, isDraft: false},
            sort: "-updatedAt"
          }),
          ctx.call('folders.find', {
            query: {workspaceId: id, isDeleted: false, _id: {$in: foldersShareWithMe.map(item => item.folderId)}},
            sort: "-updatedAt",
          }),
          ctx.call('projects.find', {
            query: {
              workspaceId: id,
              isDeleted: false,
              isDraft: false,
              _id: {$in: projectsShareWithMe.map(item => item.projectId)}
            },
            sort: "-updatedAt",
          }),
          ctx.call('mySaved.find', {query: {userId: ctx.meta.user?._id}}),
          ctx.call('projects.find', {
            query: {workspaceId: id, isDeleted: false, isDraft: false, ownerId: {$ne: ctx.meta.user?._id}}
          }),
          ctx.call('folders.find', {
            query: {workspaceId: id, isDeleted: false, ownerId: {$ne: ctx.meta.user?._id}}
          }),
        ]);

        const allProjects = notNormalUser ? [...projects, ...orgProjects] : [...projects, ...sharedProjects];
        const allFolders = notNormalUser ? [...folders, ...orgFolders] : [...folders, ...sharedFolders];

        const mapMySaved = Object.fromEntries(mySaved.map((item) => [item.folderId?._id || item.projectId?._id, item]));
        const folderIds = allFolders.map(folder => folder._id);
        const projectsBelongFolders = await ctx.call('projects.find', {
          query: {folderId: {$in: folderIds}, isDeleted: false},
          populate: []
        });
        const projectGroupByFolder = this.groupBy(projectsBelongFolders, 'folderId');
        const projectsFiltered = allProjects.filter(project => !project.folderId || !folderIds.includes(project?.folderId?._id.toString()));

        allFolders.forEach((folder) => {
          folder.projects = projectGroupByFolder[folder._id]?.length || 0;
        });

        const allInWorkspace = [...allFolders, ...projectsFiltered];

        allInWorkspace.forEach(item => item.isSaved = !!mapMySaved[item._id.toString()]);

        const sortParams = ctx.params.sort || "-updatedAt";
        const isDescending = sortParams.startsWith('-');
        const sortField = isDescending ? sortParams.split('-')[1] : sortParams;

        let list = allInWorkspace.sort((a, b) => {
          if (a[sortField] === undefined) return -1;
          if (b[sortField] === undefined) return 1;
          return a[sortField] - b[sortField];
        });

        if (isDescending) {
          list.reverse();
        }

        if (sortField === 'name' || sortField === '-name') {
          list.sort((a, b) => {
            const nameA = a.projectName || a.folderName;
            const nameB = b.projectName || b.folderName;
            return nameA.localeCompare(nameB, undefined, {numeric: true, sensitivity: 'base'});
          });
          if (sortField === '-name') {
            list.reverse();
          }
        }

        return !!limit ? list.slice(0, limit) : list;
      }
    },
    workspaceByUser: {
      rest: "GET /availableForUser",
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const [myWorkspace, orgWorkspaces] = await Promise.all([
          this.adapter.findOne({userId: user._id, isDeleted: false}),
          user.organizationId ?
            ctx.call('workspaces.find', {
              query: {organizationId: user.organizationId, isDeleted: false},
              populate: this.settings.populateOptions,
            })
              .then(orgWorkspace => Promise.all(
                orgWorkspace.map(workspace => ctx.call('workspaces.details', {id: workspace._id}))
              ))
            : []
        ]);
        const myWorkspaceTransform = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, myWorkspace);
        const [myProjects, myFolders] = await Promise.all([
          ctx.call('projects.find', {
            query: {workspaceId: myWorkspaceTransform?._id, isDeleted: false, isDraft: false},
            sort: "-updatedAt",
          }),
          ctx.call('folders.find', {
            query: {workspaceId: myWorkspaceTransform?._id, isDeleted: false},
            sort: "-updatedAt",
          })
        ]);
        if (!myWorkspaceTransform) return [];
        myWorkspaceTransform.projects = myProjects.length;
        myWorkspaceTransform.folders = myFolders.length;
        myWorkspaceTransform.members = 1;

        return [myWorkspaceTransform, ...orgWorkspaces];
      }
    },
    organizationWorkspaces: {
      rest: "GET /organization",
      auth: "required",
      async handler(ctx) {
        const userId = ctx.meta.user?._id;

        return await ctx.call('workspaces.find', {
          query: {
            $or: [{userId}, {organizationId: ctx.meta.user?.organizationId}],
            type: 'ORGANIZATIONAL',
            isDeleted: false
          },
          populate: this.settings.populateOptions
        });

      }
    },
    details: {
      rest: "GET /:id/details",
      auth: "required",
      async handler(ctx) {
        const {id} = ctx.params;
        const workspace = await ctx.call('workspaces.get', {id, populate: this.settings.populateOptions});

        const [foldersShareWithMe, projectsShareWithMe] = await Promise.all([
          ctx.call('share.find', {
            query: {type: "FOLDER", userId: ctx.meta.user?._id, isDeleted: false},
            populate: []
          }),
          ctx.call('share.find', {
            query: {type: "PROJECT", userId: ctx.meta.user?._id, isDeleted: false},
            populate: []
          })
        ]);

        const [folders, projects, folderShare, projectShare, members] = await Promise.all([
          ctx.call('folders.count', {
            query: {workspaceId: id, isDeleted: false, ownerId: ctx.meta.user?._id}
          }),
          ctx.call('projects.count', {
            query: {workspaceId: id, isDeleted: false, ownerId: ctx.meta.user?._id, isDraft: false}
          }),
          ctx.call('folders.count', {
            query: {
              workspaceId: id, isDeleted: false,
              _id: {$in: foldersShareWithMe.map(({folderId}) => folderId)}
            }
          }),
          ctx.call('projects.count', {
            query: {
              workspaceId: id, isDeleted: false, isDraft: false,
              _id: {$in: projectsShareWithMe.map(({projectId}) => projectId)}
            }
          }),
          workspace.organizationId
            ? ctx.call('users.count', {query: {organizationId: workspace.organizationId._id, isDeleted: false}})
            : 1
        ]);
        return {
          ...workspace,
          folders: folders + folderShare,
          projects: projects + projectShare,
          members
        };
      }
    },
    getOneByUser: {
      rest: "GET /getOneByUser",
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {user} = ctx.meta;
        return this.adapter.findOne({userId: user._id, isDeleted: false});
      }
    },
    getOneByOrganization: {
      rest: "GET /getOneByOrganization",
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {user} = ctx.meta;
        return this.adapter.findOne({organizationId: user.organizationId, isDeleted: false});
      }
    },
  },

  /**
   * Events
   */
  events: {
    async "organization.created"(payload, sender, event) {
      this.logger.info("payload", payload, sender, event);
      const workspaceObj = {organizationId: payload?._id, type: 'ORGANIZATIONAL'};
      return this.adapter.insert(workspaceObj);
    },
    async "organization.removed"(payload, sender, event) {
      const workspace = await this.adapter.findOne({organizationId: ObjectId(payload?._id)});
      if (!workspace) return;

      return this.adapter.updateById(workspace._id, {isDeleted: true});
    },
    async "user.registered"(payload, sender, event) {
      this.logger.info("payload", payload, sender, event);
      const workspaceObj = {userId: payload._id, type: 'PERSONAL'};
      return this.adapter.insert(workspaceObj);
    },
  },

  /**
   * Methods
   */
  methods: {
    async checkPermission(ctx) {
      const {meta, params} = ctx;
      const {user} = meta;
      if (user.isSystemAdmin) return;

      delete ctx.params.type;
      delete ctx.params.organizationId;
      delete ctx.params.userId;
      delete ctx.params.isDeleted;

      const workspace = await this.adapter.findOne({_id: params.id});
      if (!workspace) {
        throw new MoleculerClientError(i18next.t("error_workspace_not_found"), 404);
      }

      const isPersonalWorkspace = workspace.type === 'PERSONAL' && workspace?.userId.toString() === user._id.toString();
      const isOrgAdmin = workspace.type === 'ORGANIZATIONAL' && workspace?.organizationId.toString() === user?.organizationId.toString() && user?.role === 'admin';

      if (!(isPersonalWorkspace || isOrgAdmin)) {
        throw new MoleculerClientError(i18next.t("error_permission_denied"), 403);
      }
    },
    async seedDB() {
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
