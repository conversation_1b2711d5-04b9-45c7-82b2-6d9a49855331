const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { WORKSPACE, USER, ORGANIZATION } = require("../../constants/dbCollections");

const folderSchema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: USER },
    organizationId: { type: Schema.Types.ObjectId, ref: ORGANIZATION },
    type: {
      type: String,
      enum: ["ORGANIZATIONAL", "PERSONAL"]
    },
    description: { type: Schema.Types.Mixed },
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(WORKSPACE, folderSchema, WORKSPACE);
