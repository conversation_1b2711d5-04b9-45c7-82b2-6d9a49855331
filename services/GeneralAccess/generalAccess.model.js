const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { GENERAL_ACCESS, FOLDER, PROJECT, ORGANIZATION, USER, SPEAKING_SESSIONS } = require("../../constants/dbCollections");

const generalAccessSchema = new Schema(
  {
    permission: {
      type: String,
      enum: ["VIEWER", "EDITOR"],
    },
    organizationId: { type: Schema.Types.ObjectId, ref: ORGANIZATION },
    userId: { type: Schema.Types.ObjectId, ref: USER },
    folderId: { type: Schema.Types.ObjectId, ref: FOLDER },
    projectId: { type: Schema.Types.ObjectId, ref: PROJECT },
    sessionId: { type: Schema.Types.ObjectId, ref: SPEAKING_SESSIONS },
    typeAccess: {
      type: String,
      enum: ["ANYONE_WITH_LINK", "ORGANIZATIONAL", "RESTRICTED"],
      default: "RESTRICTED"
    },
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(GENERAL_ACCESS, generalAccessSchema, GENERAL_ACCESS);
