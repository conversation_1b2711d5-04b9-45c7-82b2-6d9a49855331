const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./gptAPIKey.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const AuthRole = require("../../mixins/authRole.mixin");


module.exports = {
  name: "apikeys",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },

  hooks: {
    after: {
      async list(ctx, res) {
        res.rows.forEach(r => {
          r.apiKey = this.convertKey(r.apiKey);
        });
        return res;
      },
      "getAllWithoutPagination": "convertAPIKeyForList",
      "get": "convertAPIKeyForObj",
    },
  },

  actions: {},
  methods: {
    convertAPIKeyForList(ctx, res) {
      return res.map(r => {
        const {apiKey} = r;
        r.apiKey = this.convertKey(apiKey);
        return r;
      });
    },
    convertAPIKeyForObj(ctx, res) {
      Object.values(res).forEach(r => {
        const {apiKey} = r;
        r.apiKey = this.convertKey(apiKey);
      });
      return res;
    },

    convertKey(key) {
      const length = key.length;
      const third = Math.floor(length / 3);
      if (third === 0) return key.replace(/./g, '*');

      const prefix = key.slice(0, third);
      const suffix = key.slice(-third);
      const middle = '*'.repeat(length - 2 * third);

      return `${prefix}${middle}${suffix}`;
    }

  },
  events: {},
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
