const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { FOLDER, USER, WORKSPACE } = require("../../constants/dbCollections");

const folderSchema = new Schema(
  {
    folderName: { type: Schema.Types.Mixed, required: true, validate: /\S+/ },
    ownerId: { type: Schema.Types.ObjectId, required: true, ref: USER },
    workspaceId: { type: Schema.Types.ObjectId, ref: WORKSPACE },
    folderId: { type: Schema.Types.ObjectId, ref: FOLDER },
    description: { type: Schema.Types.Mixed },
    lastModified: { type: Date, default: Date.now },
    type: {
      type: String,
      enum: ["EXAM_SCHOOL", "EXAM_IELTS", "NORMAL"],
      default: "NORMAL",
    },
    code: { type: String },
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

folderSchema.index({ ownerId: 1, workspaceId: 1 });
module.exports = mongoose.model(FOLDER, folderSchema, FOLDER);
