"use strict";

const FolderModel = require("./folder.model");
const DbMongoose = require("../../mixins/dbMongo.mixin");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const { USER_SERVICE } = require("../users");
const { PERMISSION_ACCESS } = require("../../constants/constant");
const i18next = require("i18next");
const { MoleculerClientError } = require("moleculer").Errors;
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");

module.exports = {
  name: "folders",
  mixins: [DbMongoose(FolderModel), BaseService, FunctionsCommon, AuthRole],
  /**
   * Settings
   */
  settings: {
    // Validator for the `create` & `insert` actions.
    entityValidator: {},
    populates: {
      "ownerId": USER_SERVICE.get,
      "workspaceId": 'workspaces.get',
    },
    populateOptions: ["ownerId", "workspaceId"],
  },
  hooks: {
    before: {
      "*": "checkPermission",
      "copy|move|remove|update|detail|permissionAccess": "checkFolderExist",
    },
    after: {
      "*": "activityLogger",
    }
  },
  /**
   * Dependencies
   */
  dependencies: ["users"],

  /**
   * Actions
   */
  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.NORMAL,
    },
    create: {
      rest: {
        method: "POST",
        path: "/"
      },
      auth: "required",
      params: {
        folderName: "string|min:1",
      },
      role: USER_CODES.NORMAL,
      activityLogger: true,
      async handler(ctx) {
        let { folderName, ownerId, workspaceId, description } = ctx.params;
        if (!ownerId) {
          ownerId = ctx.meta.user?._id?.toString();
        }
        if (!workspaceId) {
          const workspaces = await ctx.call('workspaces.find', {
            query: { userId: ctx.meta.user?._id, isDeleted: false }
          });
          workspaceId = workspaces[0]?._id;
        } else {
          const [workspaces] = await ctx.call('workspaces.find', {query: { _id: workspaceId, isDeleted: false }});
          if(workspaces?.type === "ORGANIZATIONAL") {
            const organization = await ctx.call('organizations.getOne', {id: workspaces.organizationId});
            if(organization?.active === false) {
              throw new MoleculerClientError(i18next.t("organization_already_locked"), 423);
            }
          }
        }
        const entity = { folderName, workspaceId, description, ownerId };
        await this.validateEntity(entity);
        const dataRes = await this.adapter.insert(entity);
        ctx.emit('folder.created', { folderId: dataRes._id });
        return dataRes;
      }
    },
    getAllManager: {
      rest: {
        method: "GET",
        path: "/manager",
      },
      auth: "required",
      async handler(ctx) {
        const query = ctx.params.query ? JSON.parse(ctx.params.query) : {};
        ctx.params.query = { ...query, isDeleted: false };
        ctx.params.populate = ctx.params.populate || this.settings.populateOptions;
        const folders = await ctx.call('folders.find', ctx.params);
        const mcallActions = folders.map(folder => {
          return { action: 'projects.count', params: { query: { folderId: folder._id, isDeleted: false } } };
        });
        const mcallRes = await ctx.mcall(mcallActions);
        for (let i = 0; i < folders.length; ++i) {
          folders[i].projects = mcallRes[i];
        }
        return folders;
      }
    },
    copy: {
      rest: {
        method: "POST",
        path: "/copy",
      },
      auth: "required",
      permission: 'VIEWER',
      activityLogger: true,
      async handler(ctx) {
        const { folderId } = ctx.params;
        const folderObj = await this.adapter.findOne({ _id: folderId });
        const { folderName, description } = folderObj;
        const workspaces = await ctx.call('workspaces.find', {
          query: { userId: ctx.meta.user?._id, isDeleted: false }
        });
        const folderCopy = {
          folderName: `${ folderName } (Copy)`,
          ownerId: ctx.meta?.user?._id,
          workspaceId: workspaces[0]?._id,
          description
        };

        const newFolder = await this.adapter.insert(folderCopy);
        await ctx.emit('folder.created', { folderId: newFolder._id });
        await ctx.emit('folder.copied', { oldFolderId: folderObj._id, newFolder });
        return this.transformDocuments(ctx, { populate: ['ownerId'] }, newFolder);
      }
    },
    move: {
      rest: {
        method: "PUT",
        path: "/:id/move",
      },
      auth: "required",
      permission: 'EDITOR',
      activityLogger: true,
      async handler(ctx) {
        const entity = ctx.params;
        const afterMove = await this.adapter.updateById(entity.id, entity);
        return this.transformDocuments(ctx, { populate: ['ownerId'] }, afterMove);
      }
    },
    remove: {
      rest: "DELETE /:id",
      auth: "required",
      params: {
        id: "string",
      },
      role: USER_CODES.NORMAL,
      activityLogger: true,
      // permission: 'EDITOR',
      /** @param {Context} ctx */
      async handler(ctx) {

        const { id } = ctx.params;
        const userId = ctx.meta.user?._id;
        const folder = await this.adapter.findOne({ _id: id });
        const isOwner = folder?.ownerId?.toString() === userId;

        const workspace = await ctx.call('workspaces.get', {id: folder?.workspaceId?.toString()});
        if(workspace?.type === "ORGANIZATIONAL") {
          const organization = await ctx.call('organizations.getOne', {id: workspace?.organizationId});
          if(!organization) {
            throw new MoleculerClientError(i18next.t("error_organization_not_found"), 404);
          }
          if(organization?.active === false) {
            throw new MoleculerClientError(i18next.t("dont_have_permission_folder"), 403);
          }
        }

        if (!isOwner) {
          const projects = await ctx.call('projects.find', {
            query: {
              folderId: id,
              isDeleted: false
            }
          });
          const promises = projects.map(project => {
            this.broker.call('share.unshare', { projectId: project._id, userId });
            this.broker.emit('removeProject', { projectId: project._id, userId });
          });
          promises.push(await this.broker.call('share.unshare', { folderId: id, userId }));
          await Promise.all(promises);
          return folder;
        }

        const dataRes = await this.adapter.updateById(id, { isDeleted: true }, { new: true });
        await ctx.emit('folder.deleted', id);
        return dataRes;
      }
    },
    update: {
      rest: {
        method: "PUT",
        path: "/:id",
      },
      role: USER_CODES.NORMAL,
      auth: "required",
      permission: 'EDITOR',
      activityLogger: true,
      async handler(ctx) {
        return ctx;
      }

    },
    permissionAccess: {
      rest: {
        method: "GET",
        path: "/:id/permission",
      },
      auth: "required",
      async handler(ctx) {
        const { id } = ctx.params;
        const { user } = ctx.meta;

        const folder = await this.adapter.findOne({ _id: id });

        if (!folder) {
          throw new MoleculerClientError(i18next.t("error_folder_not_found"), 404);
        }

        let isLock = false;
        const workspace = await ctx.call('workspaces.get', {id: folder.workspaceId.toString()});
        if(workspace?.type === "ORGANIZATIONAL") {
          const organization = await ctx.call('organizations.getOne', {id: workspace.organizationId});
          isLock = organization?.active === false;
        }

        if (folder?.ownerId.toString() === ctx.meta.user?._id.toString()) {
          if(isLock) {
            return PERMISSION_ACCESS.VIEWER;
          }
          return PERMISSION_ACCESS.OWNER;
        }

        if (user?.role !== "normal" && user?.organizationId?.toString() === workspace?.organizationId?.toString()) {
          if(isLock) {
            return PERMISSION_ACCESS.VIEWER;
          }
          return PERMISSION_ACCESS.EDITOR;
        }

        const [shared, generalAccess] = await Promise.all([
          this.broker.call('share.find', { query: { folderId: id, isDeleted: false, userId: user?._id } }),
          this.broker.call('generalAccess.find', {
            query: { folderId: id, isDeleted: false },
            populate: []
          })
        ]);
        const sharedPermissions = this.checkSharedPermission(shared);
        const generalPermissions = this.checkGeneralAccessPermission(user, generalAccess);

        if (sharedPermissions === PERMISSION_ACCESS.NO_PERMISSION && [PERMISSION_ACCESS.EDITOR, PERMISSION_ACCESS.VIEWER].includes(generalPermissions)) {
          await ctx.call('share.createGeneralAccess', {
            folderId: id,
            userId: user?._id,
            permission: generalPermissions,
            type: "FOLDER",
            isGeneral: true
          });
        }

        const combinedPermissions = [sharedPermissions, generalPermissions];
        if (combinedPermissions.includes(PERMISSION_ACCESS.EDITOR)) {
          if(isLock) {
            return PERMISSION_ACCESS.VIEWER;
          }
          return PERMISSION_ACCESS.EDITOR;
        }
        if (combinedPermissions.includes(PERMISSION_ACCESS.VIEWER)) {
          return PERMISSION_ACCESS.VIEWER;
        }
        return PERMISSION_ACCESS.NO_PERMISSION;
      },
    },
    detail: {
      rest: {
        method: "GET",
        path: "/:id/detail",
      },
      auth: "required",
      permission: 'VIEWER',
      async handler(ctx) {
        const { id } = ctx.params;
        const mySavedCount = await ctx.call('mySaved.count', {
          query: { folderId: id, userId: ctx.meta.user?._id }
        });
        const folder = await this.adapter.findById(id);
        const folderTransformed = await this.transformDocuments(ctx, { populate: ["ownerId", "workspaceId.userId", "workspaceId.organizationId"] }, folder);
        return { ...folderTransformed, isSaved: mySavedCount > 0, permission: ctx.params.permission };
      }
    },
    available: {
      rest: {
        method: "GET",
        path: "/available",
      },
      auth: "required",
      async handler(ctx) {
        const { _id: userId, organizationId } = ctx.meta.user;
        const myFolders = await ctx.call("folders.find", {
          query: {
            ownerId: userId,
            isDeleted: false,
            type: { $nin: ["EXAM_SCHOOL", "EXAM_IELTS"] }
          },
          populate: ["ownerId"]
        });
        // return myFolders;
        const shared = await this.broker.call("share.find", {
          query: {
            userId,
            isDeleted: false,
            permission: "EDITOR",
            type: 'FOLDER'
          }
        });
        const folderShared = shared.map(share => share.folderId);

        if (organizationId) {
          const orgWorkspace = await ctx.call("workspaces.getOneByOrganization");

          const orgFolder = await ctx.call("folders.find", {
            query: {
              workspaceId: orgWorkspace._id,
              ownerId: { $ne: userId },
              isDeleted: false,
              type: { $nin: ["EXAM_SCHOOL", "EXAM_IELTS"] }
            },
          });
          return [myFolders, folderShared, orgFolder].flat();
        }

        return [myFolders, folderShared].flat();

      }
    },

    getExamSchoolFolder: {
      rest: {
        method: "GET",
        path: "/getExamSchoolFolder",
      },
      auth: "required",
      async handler(ctx) {
        const { _id: userId, organizationId } = ctx.meta.user;

        // Parallelize the independent asynchronous calls
        const [myFolders, systemTemplate] = await Promise.all([
          ctx.call("folders.find", {
            query: {
              ownerId: userId,
              type: "EXAM_SCHOOL",
              isDeleted: false
            }
          }),
          ctx.call("templates.find", {
            query: {
              folderCode: { $exists: true },
              type: "SYSTEM",
              isDeleted: false
            }
          })
        ]);

        const allTemplatePromise = ctx.call("templates.find", {
          query: {
            folderId: { $in: myFolders.map(folder => folder._id) },
            isDeleted: false
          }
        });

        let orgTemplatePromise = Promise.resolve([]);

        if (organizationId) {
          orgTemplatePromise = ctx.call("templates.find", {
            query: {
              folderCode: { $exists: true },
              organizationId,
              isDeleted: false
            }
          });
        }

        const [allTemplate, orgTemplate] = await Promise.all([allTemplatePromise, orgTemplatePromise]);

        if (organizationId) {
          systemTemplate.push(...orgTemplate);
        }

        const groupTemplateByFolder = this.groupBy(allTemplate, 'folderId');
        const groupTemplateByFolderCode = this.groupBy(systemTemplate, 'folderCode');

        myFolders.forEach(folder => {
          folder.numberOfTemplate = (groupTemplateByFolder[folder._id]?.length || 0) + (groupTemplateByFolderCode[folder.code]?.length || 0);
          folder.grade = Number(folder.code.split('_')[1]);
        });

        return myFolders.sort((a, b) => a.grade - b.grade);
      }
    },

    insertManyExam: {
      rest: {
        method: "POST",
        path: "/insertManyExam",
      },
      auth: "required",
      async handler(ctx) {
        const { ownerId } = ctx.params;
        const folders = Array.from({ length: 10 }, (_, index) => ({
          ownerId,
          type: "EXAM_SCHOOL",
          isDeleted: false,
          folderName: `Bài kiểm tra lớp ${ index + 3 }`,
          code: `grade_${ index + 3 }`,
          createdAt: new Date(),
          updatedAt: new Date()
        }));

        return await this.adapter.insertMany(folders);
      }
    }
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
    async checkPermission(context) {
      const { action, params } = context;
      const { id, folderId } = params;

      if (action?.permission) {
        const permissionAccess = await context.call('folders.permissionAccess', { id: id || folderId });
        const { permission } = action;

        const isEditAccess = permission === PERMISSION_ACCESS.EDITOR;
        const isViewAccess = permission === PERMISSION_ACCESS.VIEWER;
        const noEditAccess = isEditAccess && !this.editAccess(permissionAccess);
        const noViewAccess = isViewAccess && !this.hasViewAccess(permissionAccess);

        if (noEditAccess || noViewAccess) {
          throw new MoleculerClientError(i18next.t("dont_have_permission_folder"), 403, "FORBIDDEN");
        }
        context.params.permission = permissionAccess;
      }
    },
    async activityLogger(context, res) {
      const { action, params } = context;

      if (action.activityLogger && res && params?.workspaceId) {
        const workspace = await context.call('workspaces.get', {
          id: params?.workspaceId.toString(),
          isDeleted: false
        });

        if (workspace.type === "ORGANIZATIONAL") {
          context.emit('activities.logger', {
            metadata: {
              permission: params?.permission,
              copyFolderId: params?.folderId
            },
            isDeleted: res?.isDeleted,
            folderId: params?.id || res?._id,
            action: action.rawName
          });
        }
      }
      return res;
    },
    checkSharedPermission(shared) {
      if (!shared.length) {
        return PERMISSION_ACCESS.NO_PERMISSION;
      }

      if (shared.some(item => item.permission === PERMISSION_ACCESS.EDITOR)) {
        return PERMISSION_ACCESS.EDITOR;
      }

      return PERMISSION_ACCESS.VIEWER;
    },
    checkGeneralAccessPermission(user, generalAccess) {
      const firstGeneralAccess = generalAccess[0];
      const typeAccess = firstGeneralAccess?.typeAccess;
      if (typeAccess === "ANYONE_WITH_LINK") {
        return firstGeneralAccess.permission;
      } else if (typeAccess === "ORGANIZATIONAL") {
        return this.checkOrganizationPermission(user, firstGeneralAccess);
      } else {
        return PERMISSION_ACCESS.NO_PERMISSION;
      }
    },
    checkOrganizationPermission(user, access) {
      const userOrganizationId = user.organizationId?.toString();
      const accessOrganizationId = access.organizationId.toString();
      if (userOrganizationId && userOrganizationId === accessOrganizationId) {
        return access.permission;
      }
      return PERMISSION_ACCESS.NO_PERMISSION;
    },
    async checkFolderExist(context) {
      const { params } = context;
      const folderId = params?.folderId || params?.id;
      const folder = folderId ? await this.adapter.findById(folderId) : {};
      if (folder.isDeleted === true) {
        throw new MoleculerClientError(i18next.t("folder_was_deleted"), 404);
      }
    },

    async initFolderExam() {
      const users = await this.broker.call('users.find', { query: { isDeleted: false } });
      if (!users.length) return;

      const folderExam = await this.adapter.find({ query: { type: "EXAM_SCHOOL", isDeleted: false } });
      const usersCreatedExamSet = new Set(folderExam.map(folder => folder.ownerId.toString()));

      const usersDontCreatedExam = users.filter(user => !usersCreatedExamSet.has(user._id.toString()));

      if (usersDontCreatedExam.length) {
        await Promise.all(usersDontCreatedExam.map(user =>
          this.actions.insertManyExam({ ownerId: user._id })
        ));
      }
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
    await this.initFolderExam();
  },
};
