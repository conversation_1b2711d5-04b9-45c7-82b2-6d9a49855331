const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { CONTENT, PROJECT, TOOL } = require("../../../constants/dbCollections");

const contentSchema = new Schema(
  {
    toolId: { type: Schema.Types.ObjectId, ref: TOOL },
    contentIndex: { type: Number },
    projectId: { type: Schema.Types.ObjectId, ref: PROJECT },
    title: { type: Schema.Types.Mixed },
    description: { type: String },
    isHidden: { type: Boolean, default: false },
    lastInput: { type: Schema.Types.Mixed },
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(CONTENT, contentSchema, CONTENT);
