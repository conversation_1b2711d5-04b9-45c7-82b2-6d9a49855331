const DbMongoose = require("../../../mixins/dbMongo.mixin");
const InputModel = require("./sampleContent.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const { MoleculerClientError } = require("moleculer").Errors;

module.exports = {
  name: 'samplecontents',
  mixins: [DbMongoose(InputModel), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "toolId": 'tools.get',
    },
    populateOptions: ["toolId"],
  },

  hooks: {},

  actions: {
    getOneByTool: {
      rest: {
        method: "GET",
        path: "/:toolId/getOne",
      },
      auth: "required",
      async handler(ctx) {
        const { toolId } = ctx.params;
        const sample = await this.adapter.findOne({ toolId });
        if (!sample) {
          throw new MoleculerClientError(i18next.t("error_example_not_found"), 404, "NOT_FOUND");
        }
        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, sample);
      }
    },
    saveSampleContent: {
      rest: {
        method: "POST",
        path: "/save",
      },
      auth: "required",
      async handler(ctx) {
        const { contentId } = ctx.params;
        const content = await ctx.call("contents.get", { id: contentId });
        const response = await ctx.call("responses.find", { query: { contentId, isActivate: true, isDeleted: false } });

        if (response.length === 0 || content.isDeleted === true) {
          throw new MoleculerClientError(i18next.t("error_content_not_found"), 404, "NOT_FOUND");
        }
        const { toolId, inputId, output, headline, outputType, plaintext } = response[0];
        const input = {
          inputData: inputId.inputData,
          inputType: inputId.inputType,
        };
        const sampleObj = {
          toolId: content.toolId,
          input,
          response: {
            output, headline, outputType, plaintext
          },
          title: content.title,
          description: content.description,
        };
        const sample = await this.adapter.findOne({ toolId });
        if (sample) {
          return await this.adapter.updateById(sample._id, sampleObj);
        }
        return await this.adapter.insert(sampleObj);
      }
    }
  },
  methods: {},
  events: {}
};
