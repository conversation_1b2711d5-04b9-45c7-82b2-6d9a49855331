const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {RECENT_PROJECT, PROJECT, USER} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    userId: {type: Schema.Types.ObjectId, ref: USER},
    projectId: {type: Schema.Types.ObjectId, ref: PROJECT, required: true},
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(RECENT_PROJECT, schema, RECENT_PROJECT);
