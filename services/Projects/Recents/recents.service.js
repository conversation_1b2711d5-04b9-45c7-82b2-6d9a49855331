const DbMongoose = require("../../../mixins/dbMongo.mixin");
const InputModel = require("./recents.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");

module.exports = {
  name: 'recents',
  mixins: [DbMongoose(InputModel), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "projectId": {
        action: "projects.get",
      },
    },
    populateOptions: ["projectId.folderId", "projectId.ownerId", "projectId.imageId"]
  },

  hooks: {
    after: {
      find(ctx, res) {
        return res;
      }
    }
  },

  actions: {
    recently: {
      rest: {
        method: "GET",
        path: "/recently",
      },
      auth: "required",
      async handler(ctx) {
        const { query, populate, searchFields } = ctx.params;
        const parsedQuery = query ? JSON.parse(query) : {};
        const updatedQuery = { ...parsedQuery, isDeleted: { $ne: true } };

        const finalQuery = searchFields ? this.convertSearchFields(searchFields, updatedQuery) : updatedQuery;
        const finalPopulate = populate || this.settings.populateOptions;

        const result = await ctx.call(`${ this.name }.find`, {
          query: finalQuery,
          populate: finalPopulate,
          sort: '-updatedAt',
          limit: 8,
          lean: true
        });
        const projectSavedPromise = result.map(async ({ projectId }) => {
          return await ctx.call('mySaved.count', { query: { userId: ctx.meta.user?._id, projectId: projectId?._id } });
        });
        const projectSaved = await Promise.all(projectSavedPromise);
        return result.map(({ projectId, updatedAt }, index) => ({
          ...projectId,
          isSaved: projectSaved[index] > 0,
          updatedAt
        }));
      }
    },
    removeMany: {
      rest: {
        method: "POST",
        path: "/removeMany",
      },
      auth: "required",
      async handler(ctx) {
        const { projectIds, userId } = ctx.params;
        return await this.adapter.removeMany({ projectId: projectIds, userId });
      }
    }

  },
  methods: {},
  events: {
    async recentProject({ projectId, userId }) {
      const project = await this.broker.call("projects.get", { id: projectId?.toString() });
      if (project.isDraft) {
        return;
      }
      const recentProject = await this.adapter.findOne({ projectId, userId });
      if (!recentProject) {
        await this.adapter.insert({ projectId, userId });
      } else {
        await this.adapter.updateById(recentProject._id, { $set: { updatedAt: new Date() } });
      }
    },
    async removeProject({ projectId, userId }) {
      return await this.adapter.removeMany({ projectId, userId });
    },
    async "project.deleted"(payload) {
      this.adapter.removeMany({ projectId: payload });
    },
  }
};
