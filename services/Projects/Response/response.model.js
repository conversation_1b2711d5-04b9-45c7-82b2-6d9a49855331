const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {RESPONSE, CONTENT, INPUT, TOOL} = require("../../../constants/dbCollections");

const responseSchema = new Schema(
  {
    contentId: {type: Schema.Types.ObjectId, ref: CONTENT},
    inputId: {type: Schema.Types.ObjectId, ref: INPUT},
    toolId: {type: String},
    isActivate: {type: Boolean},
    state: {
      type: String,
      enum: ["processing", "done", "error"],
    },
    output: {type: Schema.Types.Mixed, required: true},
    outputType: {type: String},
    plaintext: {type: String},
    rating: {
      type: String,
      enum: ["like", "dislike"],
    },
    headline: {type: Schema.Types.Mixed},
    lastMessages: {type: Schema.Types.Mixed},
    gptModel: {type: String},
    completionTokens: {type: Number},
    promptTokens: {type: Number},
    totalTokens: {type: Number},
    audioDuration: {type: Number},
    examOrder: {type: Number, min: 1},

    canceledSubmit: {type: Boolean, default: false},
    previousOutput: {type: Schema.Types.Mixed},
    previousState: {
      type: String,
      enum: ["processing", "done", "error"],
    },
    isNewResponse: {type: Boolean, default: true},
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);
responseSchema.index({contentId: 1});
module.exports = mongoose.model(RESPONSE, responseSchema, RESPONSE);
