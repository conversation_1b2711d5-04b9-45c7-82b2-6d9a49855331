const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {CATEGORY, TOOL, TAG} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    name: {type: String, required: true},
    toolId: {type: Schema.Types.ObjectId, ref: TOOL},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(CATEGORY, schema, CATEGORY);
