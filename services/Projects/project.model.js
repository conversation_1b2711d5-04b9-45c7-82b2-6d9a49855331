const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { PROJECT, FOLDER, WORKSPACE, IMAGE, USER } = require("../../constants/dbCollections");

const schema = new Schema(
  {
    projectName: { type: Schema.Types.Mixed, required: true, validate: /\S+/ },
    isFavorite: { type: Boolean, default: false },
    folderId: { type: Schema.Types.ObjectId, ref: FOLDER },
    workspaceId: { type: Schema.Types.ObjectId, ref: WORKSPACE },
    imageId: { type: Schema.Types.ObjectId, ref: IMAGE },
    ownerId: { type: Schema.Types.ObjectId, required: true, ref: USER },
    description: { type: Schema.Types.Mixed },

    isTemplate: { type: Boolean, default: false },
    isPreview: { type: Boolean, default: false },
    isShowPlainText: { type: <PERSON>olean, default: false },
    isDeleted: { type: Boolean, default: false },
    isDraft: { type: Boolean, default: false },
    lastModified: { type: Date },
    lastModifiedBy: { type: Schema.Types.ObjectId, ref: USER },
    lastViewed: { type: Date },
    lastViewedBy: { type: Schema.Types.ObjectId, ref: USER },
    type: {
      type: String,
      enum: ["NORMAL", "EXAM_SCHOOL", "EXAM_IELTS", "MARK_TEST_SCHOOL", "MARK_TEST_IELTS"],
      default: "NORMAL",
    },
    examCode: String,
    commonOptions: { type: Schema.Types.Mixed },
    activeExam: Number,
    numberOfExams: { type: Number, min: 0 },
    tag: [{ type: String }],
    category: [{ type: String }],
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

schema.index({ folderId: 1, ownerId: 1, workspaceId: 1 });

module.exports = mongoose.model(PROJECT, schema, PROJECT);
