const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./tags.model");
const BaseService = require("../../../mixins/baseService.mixin");
const authRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");

module.exports = {
  name: 'tags',
  mixins: [DbMongoose(Model), BaseService, authRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: []
  },

  hooks: {
    before: {}
  },

  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.NORMAL,
    },
  },
  methods: {},
  events: {},
};
