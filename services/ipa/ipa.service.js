"use strict";

const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const FileMixin = require("../../mixins/file.mixin");
const BaseService = require("../../mixins/baseService.mixin");
const Model = require("./ipa.model");
const DbMongoose = require("../../mixins/dbMongo.mixin");
const AuthRole = require("../../mixins/authRole.mixin");
const axios = require('axios');
const path = require("path");
const fs = require("fs");
const storageDir = path.join(__dirname, "storage");
const sdk = require("microsoft-cognitiveservices-speech-sdk");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;

module.exports = {
  name: "ipa",
  mixins: [Db<PERSON><PERSON><PERSON><PERSON>(Model), FunctionsCommon, BaseService, FileMixin, AuthRole],

  settings: {
    entityValidator: {},
    populates: {
      "audioFileId": 'files.get',
    },
    populateOptions: ["audioFileId"],
  },

  dependencies: [],

  actions: {
    getWordData: {
      rest: "GET /wordData",
      async handler(ctx) {
        try {
          const {word} = ctx.params;
          const checkCache = await this.adapter.findOne({word: word.toLowerCase()});
          if (checkCache) return {
            ipa: checkCache.ipa,
            audioUrl: checkCache.audioUrl,
            word
          };
          const response = await axios.get(`https://api.dictionaryapi.dev/api/v2/entries/en/${word}`);
          const ipa = response.data[0].phonetic; // Lấy phiên âm
          const audioUrl = response.data[0].phonetics.find(item => !!item.audio)?.audio; // Lấy đường dẫn âm thanh
          const wordData = this.adapter.insert({word: word.toLowerCase(), ipa, audioUrl});
          return this.transformDocuments(ctx, {}, wordData)
        } catch (error) {
          const {word} = ctx.params;
          console.error(error.message, "WORD ERROR", word);
        }
      }
    },
    wordAudio: {
      rest: "GET /wordAudio",
      async handler(ctx) {
        const {word} = ctx.params;

        const {speechKey, serviceRegion} = await this.broker.call("settings.findOne");
        const voice = "en-US-AndrewMultilingualNeural";
        const wordPath = this.getFilePath(`${word}_${voice}.wav`, this.getDirPath(storageDir));
        const speechConfig = sdk.SpeechConfig.fromSubscription(speechKey, serviceRegion);
        speechConfig.speechSynthesisLanguage = "en-US";
        speechConfig.speechSynthesisVoiceName = voice

        const audioConfig = sdk.AudioConfig.fromAudioFileOutput(wordPath);
        const synthesizer = new sdk.SpeechSynthesizer(speechConfig, audioConfig);
        if (!fs.existsSync(wordPath)) {
          let stat = fs.statSync(wordPath);
          ctx.meta.$responseHeaders = {
            "Content-Type": "audio/wav",
            "Content-Length": stat.size,
            "Content-Disposition": `attachment;filename=${word}.wav`
          };
          return fs.createReadStream(wordPath, {});
        }
        await synthesizer.speakTextAsync(
          word,
          (result) => {
            synthesizer.close();
            let stat = fs.statSync(wordPath);
            ctx.meta.$responseHeaders = {
              "Content-Type": "audio/wav",
              "Content-Length": stat.size,
              "Content-Disposition": `attachment;filename=${word}.wav`
            };
            return fs.createReadStream(wordPath, {});
          },
          (err) => {
            synthesizer.close();
            if (err) {
              console.log(err);
              return null;
            }
          }
        );
      }
    },
    wordPath: {
      rest: "GET /:word/path",
      async handler(ctx) {
        try {
          const {word} = ctx.params;

          const voice = "en-US-AndrewMultilingualNeural";
          const wordPath = this.getFilePath(`${word}_${voice}.wav`, this.getDirPath(storageDir));
          if (!fs.existsSync(wordPath)) {
            return new MoleculerClientError(i18next.t("error_data_not_found"), 404);
          }
          return wordPath
        } catch (e) {
          console.log(e);
        }
      }
    },

  },

  events: {},

  methods: {
    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },
  },

  created() {
  },

  async started() {
    this.createFolderIfNotExist(storageDir);
  },

  async stopped() {
  },
};
