"use strict";

const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const Model = require("./courses.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const i18next = require("i18next");
const { before } = require("lodash");
const {MoleculerClientError} = require("moleculer").Errors;

module.exports = {
  name: "courses",
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService, FileMixin],

  settings: {
    entityValidator: {
      name: { type: "string", min: 2, max: 255 },
      description: { type: "string", optional: true, max: 5000 },
      introduction: { type: "string", optional: true, max: 5000 },
      referenceUrls: { type: "array", optional: true, items: "string" },
      roleplayInstructionId: { type: "string", optional: true },
      referenceFiles: { type: "array", optional: true, items: "string" },
      aiPersonaId: { type: "string", optional: true },
      estimatedCallTimeInMinutes: { type: "number", optional: true, min: 0 },
      simulationType: { type: "enum", values: ["Sale", "Service", "HR", "Education", "Other"], optional: true }
    },
    populates: {
      "references": "references.get",
      "aiPersonaId": "aipersonas.get",
      "taskIds": "tasks.get",
      "organizationId": 'organizations.get',
      "createdBy": 'users.get',
      "updatedBy": 'users.get',
      "roleplayInstructionId" : "roleplayinstruction.get"
    },
    populateOptions: ["references", "aiPersonaId", "organizationId", "createdBy", "updatedBy", "taskIds"],
    fields: [
      "_id",
      "name",
      "description",
      "introduction",
      "references",
      "aiPersonaId",
      "estimatedCallTimeInMinutes",
      "simulationType",
      "taskIds",
      "organizationId",
      "roleplayInstructionId",
      "createdBy",
      "updatedBy",
      "createdAt",
      "updatedAt",
      "isDeleted"
    ],
  },

  hooks: {
    after: {
      "create|update|delete|addReferenceFile|removeReferenceFile": function (ctx, res) {
        const courseId = res._id;
        const performedBy = ctx.meta.user?._id;
        const action = ctx.action.name;
        this.logger.info(`${action} course ${courseId} by user ${performedBy}`);
        return res;
      },
    },
    before: {
      "update": async function (ctx) {
      }
    }
  },

  dependencies: ["files", "aipersonas", "organizations", "users", "references", "tasks"],

  actions: {
    updateCourseByAI: {
      rest: "POST /:id/updateByAI",
      params: {
        id: { type: "string" }, // Course ID
        userPrompt: { type: "string", optional: true } // User's specific instructions for AI
      },
      async handler(ctx) {
        const { id, userPrompt } = ctx.params;
        const user = ctx.meta.user;

        // 1. Get Course Details
        const course = await this.adapter.findById(id);
        if (!course || course.isDeleted) {
          throw new MoleculerClientError(i18next.t("error.course_not_found", "Không tìm thấy khóa học"), 404);
        }

        // Permission Check (example - adapt as needed)
        const hasPermissionToUpdate = user.isSystemAdmin ||
                                   (user.isOrgAdmin && course.organizationId && user.organizationId.toString() === course.organizationId.toString()) ||
                                   (course.createdBy && user._id.toString() === course.createdBy.toString());

        if (!hasPermissionToUpdate) {
          throw new MoleculerClientError(i18next.t("error.permission_denied", "Bạn không có quyền cập nhật khóa học này bằng AI"), 403);
        }

        // 2. Gather all reference content
        let referencesContent = "";
        if (course.references && course.references.length > 0) {
          const referenceObjects = await ctx.call("references.find", { query: { _id: { $in: course.references } } });
          referencesContent = referenceObjects.map(ref => ref.content || ref.url || ref.name).join("\n---\n");
        }

        // 3. Create/Update AI Persona
        this.logger.info(`Updating AI Persona for course ${id} using AI...`);
        let aiPersona;
        try {
          aiPersona = await ctx.call("aipersonas.createAIPersonaFromCourseContext", {
            courseId: id,
            courseName: course.name,
            courseDescription: course.description,
            courseReferencesContent: referencesContent,
            userPrompt: userPrompt ? `Liên quan đến AI Persona: ${userPrompt}` : undefined
          });
          this.logger.info(`AI Persona ${aiPersona._id} created/updated for course ${id}.`);
        } catch (error) {
          this.logger.error(`Failed to create/update AI Persona for course ${id}:`, error);
          // Decide if this is a fatal error or if we can proceed without a new persona
          throw new MoleculerClientError("Lỗi khi AI tạo Persona: " + error.message, error.code || 500, error.type || "AI_PERSONA_ERROR");
        }

        // 4. Create Tasks
        this.logger.info(`Generating tasks for course ${id} using AI...`);
        let generatedTasks;
        // Construct a comprehensive prompt for task generation
        let taskPrompt = `Tên khóa học: ${course.name}`;
        if (course.description) taskPrompt += `\nMô tả: ${course.description}`;
        if (referencesContent) taskPrompt += `\nNội dung tham khảo chính: ${referencesContent.substring(0, 1500)}...`; // Truncate for brevity
        if (aiPersona) taskPrompt += `\nAI Persona được tạo: ${aiPersona.name} (Vai trò: ${aiPersona.role}, Bối cảnh: ${aiPersona.personaBackground.substring(0,200)}...)`;
        if (userPrompt) taskPrompt += `\nYêu cầu cụ thể từ người dùng cho tasks: ${userPrompt}`;

        try {
          const taskCreationResult = await ctx.call("tasks.createTasksFromPrompt", {
            courseId: id,
            prompt: taskPrompt,
            aiPersonaId: aiPersona ? aiPersona._id : undefined
          });
          generatedTasks = taskCreationResult.tasks;
          this.logger.info(`${generatedTasks.length} tasks generated for course ${id}.`);
        } catch (error) {
          this.logger.error(`Failed to generate tasks for course ${id}:`, error);
          // Decide if this is fatal or can proceed
          throw new MoleculerClientError("Lỗi khi AI tạo Tasks: " + error.message, error.code || 500, error.type || "AI_TASK_ERROR");
        }

        // 5. Update Course with new AI Persona ID and Task IDs
        const updatePayload = {
          aiPersonaId: aiPersona ? aiPersona._id : course.aiPersonaId, // Use new persona, or keep old if creation failed but we decided to proceed
          updatedBy: user._id,
          updatedAt: new Date()
        };

        // Add new tasks, avoid duplicates if re-running
        const newTaskIds = generatedTasks.map(t => t._id);
        const existingTaskIds = course.taskIds ? course.taskIds.map(tid => tid.toString()) : [];
        const allTaskIds = [...new Set([...existingTaskIds, ...newTaskIds])]; // Combine and unique
        updatePayload.taskIds = allTaskIds;

        const updatedCourse = await this.adapter.updateById(id, { $set: updatePayload });

        this.broker.emit("courses.updatedByAI", { course: updatedCourse, user, aiPersona, generatedTasks });
        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, updatedCourse);
      }
    },

    // Tạo khóa học mới
    createCourse: {
      rest: "POST /",
      params: {
        name: { type: "string", min: 2, max: 255 },
        description: { type: "string", optional: true, max: 5000 },
        introduction: { type: "string", optional: true, max: 5000 },
        referenceUrls: { type: "array", optional: true, items: "string" },
        referenceFiles: { type: "array", optional: true, items: "string" },
        aiPersonaId: { type: "string", optional: true },
        estimatedCallTimeInMinutes: { type: "number", optional: true, min: 0 },
        simulationType: { type: "enum", values: ["Sale", "Service", "HR", "Education", "Other"], optional: true },
        taskIds: { type: "array", optional: true, items: "string" },
        organizationId: { type: "string", optional: true }
      },
      async handler(ctx) {
        const {
          name,
          description,
          introduction,
          references,
          aiPersonaId,
          estimatedCallTimeInMinutes,
          simulationType,
          taskIds,
          organizationId
        } = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t("error.unauthorized", "Bạn chưa đăng nhập"), 401);
        }

        // Kiểm tra quyền tạo khóa học (system admin hoặc org admin)
        const hasPermission = user.isSystemAdmin ||
                             (user.isOrgAdmin && (!organizationId || user.organizationId.toString() === organizationId.toString()));

        if (!hasPermission) {
          throw new MoleculerClientError(i18next.t("error.permission_denied", "Bạn không có quyền tạo khóa học"), 403);
        }

        // Kiểm tra AI Persona tồn tại nếu được chỉ định
        if (aiPersonaId) {
          const persona = await ctx.call("aipersonas.get", { id: aiPersonaId })
            .catch(() => null);

          if (!persona) {
            throw new MoleculerClientError(i18next.t("error.persona_not_found", "AI Persona không tồn tại"), 404);
          }
        }

        // Kiểm tra tasks nếu được chỉ định
        if (taskIds && taskIds.length > 0) {
          // Có thể kiểm tra tính hợp lệ của tasks ở đây
        }

        const courseData = {
          name,
          description,
          introduction,
          references: references || [],
          aiPersonaId,
          estimatedCallTimeInMinutes,
          simulationType,
          taskIds: taskIds || [],
          organizationId: organizationId || user.organizationId,
          createdBy: user._id,
          updatedBy: user._id,
          status: "draft"
        };

        const course = await this.adapter.insert(courseData);
        this.broker.emit("courses.created", { course, user });
        return this.transformDocuments(ctx, {}, course);
      },
    },

    // Xóa khóa học (xóa mềm)
    deleteCourse: {
      rest: "DELETE /:id",
      params: {
        id: { type: "string" }
      },
      async handler(ctx) {
        const {id} = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t("error.unauthorized", "Bạn chưa đăng nhập"), 401);
        }

        // Lấy thông tin khóa học để kiểm tra quyền
        const course = await this.adapter.findById(id);
        if (!course || course.isDeleted) {
          throw new MoleculerClientError(i18next.t("error.course_not_found", "Không tìm thấy khóa học"), 404);
        }

        // Kiểm tra quyền xóa khóa học
        const hasPermission = user.isSystemAdmin ||
                             (user.isOrgAdmin && course.organizationId && user.organizationId.toString() === course.organizationId.toString());

        if (!hasPermission) {
          throw new MoleculerClientError(i18next.t("error.permission_denied", "Bạn không có quyền xóa khóa học này"), 403);
        }

        const updated = await this.adapter.updateById(id, {
          $set: {
            isDeleted: true,
            deletedAt: new Date(),
            updatedBy: user._id,
          }
        });

        this.broker.emit("courses.deleted", { courseId: id, user });
        return { success: true, id };
      },
    },

    // Lấy chi tiết khóa học
    getCourse: {
      rest: "GET /:id",
      params: {
        id: { type: "string" },
        withTasks: { type: "boolean", optional: true, default: false }
      },
      async handler(ctx) {
        const { id, withTasks } = ctx.params;
        const user = ctx.meta.user;

        // Lấy thông tin khóa học
        const course = await this.adapter.findOne({ _id: id, isDeleted: { $ne: true } });
        if (!course) {
          throw new MoleculerClientError(i18next.t("error.course_not_found", "Không tìm thấy khóa học"), 404);
        }

        // Tùy chọn lấy kèm nhiệm vụ chi tiết
        const populate = [...this.settings.populateOptions];
        if (withTasks) {
          populate.push("taskIds");
        }

        // Transform và trả về kết quả
        const data = await this.transformDocuments(ctx, { populate }, course);
        console.log("#############data", data);
        return data;
      }
    },

    // Lấy danh sách nhiệm vụ của khóa học
    getTasksOfCourse: {
      rest: "GET /:id/tasks",
      params: {
        id: { type: "string" }
      },
      async handler(ctx) {
        const { id } = ctx.params;

        // Lấy thông tin khóa học
        const course = await this.adapter.findOne({ _id: id, isDeleted: { $ne: true } });
        if (!course) {
          throw new MoleculerClientError(i18next.t("error.course_not_found", "Không tìm thấy khóa học"), 404);
        }

        // Nếu không có nhiệm vụ nào
        if (!course.taskIds || course.taskIds.length === 0) {
          return { tasks: [] };
        }
        console.log("#############course.taskIds", course.taskIds);
        const query = {
          _id: { $in: course.taskIds },
          isDeleted: { $ne: true }
        }
        console.log("#############query", query);
        // Lấy danh sách nhiệm vụ
        const tasks = await ctx.call("tasks.list", {
          query: JSON.stringify(query),
          sort: "orderInCourse"
        });

        return { tasks: tasks || [] };
      }
    },

    // Thêm tài liệu tham khảo vào khóa học
    addReferenceToCourse: {
      rest: "POST /:id/references/:referenceId",
      params: {
        id: { type: "string" },
        referenceId: { type: "string" }
      },
      async handler(ctx) {
        const { id, referenceId } = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t("error.unauthorized", "Bạn chưa đăng nhập"), 401);
        }

        // Lấy thông tin khóa học để kiểm tra quyền
        const course = await this.adapter.findById(id);
        if (!course || course.isDeleted) {
          throw new MoleculerClientError(i18next.t("error.course_not_found", "Không tìm thấy khóa học"), 404);
        }

        // Kiểm tra quyền cập nhật khóa học
        const hasPermission = user.isSystemAdmin ||
                           (user.isOrgAdmin && course.organizationId && user.organizationId.toString() === course.organizationId.toString());

        if (!hasPermission) {
          throw new MoleculerClientError(i18next.t("error.permission_denied", "Bạn không có quyền cập nhật khóa học này"), 403);
        }

        // Kiểm tra tài liệu tham khảo tồn tại
        const reference = await ctx.call("references.get", { id: referenceId })
          .catch(() => null);

        if (!reference) {
          throw new MoleculerClientError(i18next.t("error.reference_not_found", "Không tìm thấy tài liệu tham khảo"), 404);
        }

        // Thêm tài liệu vào danh sách tham khảo
        if (!course.references) {
          course.references = [];
        }
        if (!course.references.includes(referenceId)) {
          course.references.push(referenceId);
        }

        // Cập nhật khóa học
        const updated = await this.adapter.updateById(id, {
          $set: {
            references: course.references,
            updatedBy: user._id,
            updatedAt: new Date()
          }
        });

        return { success: true, reference, course: updated };
      }
    },

    // Xóa tài liệu tham khảo khỏi khóa học
    removeReferenceFromCourse: {
      rest: "DELETE /:id/references/:referenceId",
      params: {
        id: { type: "string" },
        referenceId: { type: "string" }
      },
      async handler(ctx) {
        const { id, referenceId } = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t("error.unauthorized", "Bạn chưa đăng nhập"), 401);
        }

        // Lấy thông tin khóa học để kiểm tra quyền
        const course = await this.adapter.findById(id);
        if (!course || course.isDeleted) {
          throw new MoleculerClientError(i18next.t("error.course_not_found", "Không tìm thấy khóa học"), 404);
        }

        // Kiểm tra quyền cập nhật khóa học
        const hasPermission = user.isSystemAdmin ||
                           (user.isOrgAdmin && course.organizationId && user.organizationId.toString() === course.organizationId.toString());

        if (!hasPermission) {
          throw new MoleculerClientError(i18next.t("error.permission_denied", "Bạn không có quyền cập nhật khóa học này"), 403);
        }

        // Xóa tài liệu khỏi danh sách tham khảo
        if (course.references && course.references.includes(referenceId)) {
          course.references = course.references.filter(id => id.toString() !== referenceId);

          // Cập nhật khóa học
          await this.adapter.updateById(id, {
            $set: {
              references: course.references,
              updatedBy: user._id,
              updatedAt: new Date()
            }
          });
        }

        return { success: true, id, referenceId };
      }
    }
  },

  events: {
    "courses.created": {
      async handler(payload) {
        this.logger.info(`New course created: ${payload.course.name}`);
      }
    },

    "courses.updated": {
      async handler(payload) {
        this.logger.info(`Course updated: ${payload.course._id}`);
      }
    },

    "courses.deleted": {
      async handler(payload) {
        this.logger.info(`Course deleted: ${payload.courseId}`);
      }
    },

    "references.created": {
      async handler(payload) {
        // Không cần xử lý gì đặc biệt khi tạo mới reference
        // Vì reference sẽ được thêm vào course thông qua action addReferenceToCourse
        this.logger.info(`New reference created: ${payload.reference._id}`);
      }
    },

    "references.updated": {
      async handler(payload) {
        // Khi reference được cập nhật, không cần thay đổi gì trong course
        this.logger.info(`Reference updated: ${payload.reference._id}`);
      }
    },

    "references.deleted": {
      async handler(payload) {
        if (payload.reference) {
          const referenceId = payload.reference._id || payload.referenceId;

          // Tìm tất cả các khóa học có chứa reference này
          const courses = await this.adapter.find({
            query: {
              references: referenceId,
              isDeleted: { $ne: true }
            }
          });

          // Xóa reference khỏi tất cả các khóa học
          for (const course of courses) {
            course.references = course.references.filter(id => id.toString() !== referenceId.toString());

            await this.adapter.updateById(course._id, {
              $set: {
                references: course.references,
                updatedAt: new Date()
              }
            });

            this.logger.info(`Removed reference ${referenceId} from course ${course._id}`);
          }
        }
      }
    },

    "tasks.created": {
      async handler(payload) {
        if (payload.task && payload.task.courseId) {
          // Thêm task ID vào mảng taskIds của course
          const courseId = payload.task.courseId;
          const taskId = payload.task._id;
          const course = await this.adapter.findById(courseId);

          if (course && !course.isDeleted) {
            if (!course.taskIds) {
              course.taskIds = [];
            }
            if (!course.taskIds.includes(taskId)) {
              course.taskIds.push(taskId);
              await this.adapter.updateById(courseId, {
                $set: { taskIds: course.taskIds }
              });
              this.logger.info(`Added task ${taskId} to course ${courseId}`);
            }
          }
        }
      }
    },

    "tasks.deleted": {
      async handler(payload) {
        if (payload.task && payload.task.courseId) {
          // Xóa task ID khỏi mảng taskIds của course
          const courseId = payload.task.courseId;
          const taskId = payload.task._id;
          const course = await this.adapter.findById(courseId);

          if (course && !course.isDeleted && course.taskIds) {
            const updatedTaskIds = course.taskIds.filter(id => id.toString() !== taskId.toString());
            await this.adapter.updateById(courseId, {
              $set: { taskIds: updatedTaskIds }
            });
            this.logger.info(`Removed task ${taskId} from course ${courseId}`);
          }
        }
      }
    }
  },

  methods: {
    /**
     * Kiểm tra quyền của user đối với khóa học
     *
     * @param {Object} user - Thông tin user
     * @param {Object} course - Thông tin khóa học
     * @param {String} permission - Loại quyền ('read', 'write', 'delete')
     * @returns {Boolean} - Có quyền hay không
     */
    hasPermission(user, course, permission = 'read') {
      if (!user) return false;

      // System admin có mọi quyền
      if (user.isSystemAdmin) return true;

      // Organization admin có quyền với khóa học trong tổ chức của mình
      if (user.isOrgAdmin && course.organizationId &&
          user.organizationId && user.organizationId.toString() === course.organizationId.toString()) {
        return true;
      }

      // Người dùng thường chỉ có quyền đọc
      if (permission === 'read') {
        // Khóa học thuộc tổ chức của user
        if (course.organizationId && user.organizationId &&
            user.organizationId.toString() === course.organizationId.toString()) {
          return true;
        }
      }

      return false;
    }
  },
};
