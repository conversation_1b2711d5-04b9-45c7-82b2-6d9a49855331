const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {GROUP_FEEDBACK, FEEDBACK} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    name: {type: String, validate: /\S+/},
    description: {type: String, validate: /\S+/},
    feedbacks: [{
      _id: false,
      index: {type: Number},
      feedbackId: {type: Schema.Types.ObjectId, ref: FEEDBACK}
    }],
    active: {type: Boolean, default: false},

    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(GROUP_FEEDBACK, schema, GROUP_FEEDBACK);
