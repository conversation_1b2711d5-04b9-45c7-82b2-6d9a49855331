const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./userFeedbacks.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const MailMixin = require("../../../mixins/mailSupport.mixin");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRole = require("../../../mixins/authRole.mixin");
const {tool} = require("@langchain/core/tools");
const {ObjectId} = require("mongoose").Types;
const {sendEmail} = require("../../../helpers/emailHelper");
const {getConfig} = require("../../../config/config");
const config = getConfig(process.env.NODE_ENV);


module.exports = {
  name: 'userFeedbacks',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole, MailMixin],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "submitFeedbackId": "submitFeedbacks.get",
      "feedbackId": "feedbacks.get",
    },
    populateOptions: ["feedbackId"]
  },

  hooks: {},

  actions: {
    submit: {
      rest: "POST /submit",
      async handler(ctx) {
        const {user} = ctx.meta;
        const {toolId, dataFeedback, projectId} = ctx.params;
        const [groupFeedback] = await ctx.call("groupFeedbacks.find", {query: {active: true}});

        if (!dataFeedback) {
          throw new MoleculerClientError(i18next.t("error_query_not_found"), 404);
        }

        if (!toolId) {
          throw new MoleculerClientError(i18next.t("error_tool_not_found"), 404);
        }

        const submitFeedback = await ctx.call("submitFeedbacks.create", {
          userId: user._id,
          toolId,
          projectId,
          groupFeedbackId: groupFeedback._id
        });

        const queryFeedback = await Promise.all(dataFeedback.map(async (item) => ({
          submitFeedbackId: submitFeedback._id,
          userId: user._id,
          projectId,
          ...item
        })));
        console.log("user", user)
        this.sendFeedbackEmail(dataFeedback, toolId, user.email, user.phone, user.fullName);
        await this.adapter.insertMany(queryFeedback);
        return submitFeedback;
      }
    },
    statisticUserFeedback: {
      async handler(ctx) {
        const lang = ctx.meta?.lang || "en";
        const {time, toolName, email, toolId, groupFeedbackId, ...otherParams} = ctx.params;
        const paramsPage = this.extractParamsPage(ctx.params)

        const queryFeedback = {
          ...(toolName && {[`toolName.${lang}`]: {$regex: toolName, $options: "i"}}),
          ...(email && {"email": {$regex: email, $options: "i"}}),
        }

        const submitFeedbacks = await ctx.call("submitFeedbacks.find", {
          query: {
            ...(groupFeedbackId && {groupFeedbackId: ObjectId(groupFeedbackId)}),
            ...(toolId && {toolId: ObjectId(toolId)}),
          }, populate: []
        })
        let submitFeedbackIds = submitFeedbacks.map(item => ObjectId(item._id));
        const queryFilter = {
          ...(time && this.extractQueryTime(ctx.params)),
          submitFeedbackId: {$in: submitFeedbackIds}
        }

        const keyFilter = Object.keys(otherParams)
          .reduce((acc, key) => {
            if (key.startsWith("feedback")) {
              let value = otherParams[key];
              const numValue = Number(value);
              if (!isNaN(numValue)) {
                value = numValue;
              } else {
                if (value.toLowerCase() === 'true') {
                  value = true;
                } else if (value.toLowerCase() === 'false') {
                  value = false;
                }
              }

              acc[key] = value;
            }
            return acc;
          }, {});


        return await this.statisticUserFeedbacks(queryFeedback, queryFilter, paramsPage, keyFilter, lang);
      }
    },
    analysisUserFeedback: {
      async handler(ctx) {
        const lang = ctx.meta?.lang || "en";
        const {time, groupFeedbackId, toolName} = ctx.params;
        const paramsPage = this.extractParamsPage(ctx.params);

        const groupFeedback = await ctx.call("groupFeedbacks.find", {
          query: groupFeedbackId ? {_id: groupFeedbackId} : {active: true}
        });
        const feedbackData = groupFeedback[0].feedbacks;
        const submitFeedbacks = await ctx.call("submitFeedbacks.find", {
          query: {groupFeedbackId: groupFeedbackId || groupFeedback[0]._id}
        });
        const submitFeedbackIds = submitFeedbacks.map(item => ObjectId(item._id));

        const queryAnalysis = {
          ...(time && this.extractQueryTime(ctx.params)),
          submitFeedbackId: {$in: submitFeedbackIds}
        }

        const queryFilter = {
          ...(toolName && {[`toolName.${lang}`]: {$regex: toolName, $options: "i"}}),
        };
        return await this.analysisSubmitFeedback(queryFilter, queryAnalysis, feedbackData, paramsPage, lang);
      }
    },

  },
  methods: {
    async sendFeedbackEmail(dataFeedback, toolId, email, phone, fullName) {

      const dataSendmail = await this.handleDataFeedBack(dataFeedback, toolId, email, phone, fullName);
      const formHtml = this.createFeedBackEmail(dataSendmail);
      let mailOptions = {
        from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
        to: config.mail.auth.user, // list of receivers
        subject: 'User Feedback Clickee', // Subject line
        html: formHtml,
      };
      sendEmail(mailOptions, (err) => {
        if (err) {
          console.log(err);
        }
      });
      this.broker.emit("mail.sent", {
        to: config.mail.auth.user,
        subject: 'User Feedback Clickee',
        status: "success",
        data: dataSendmail,
      });
    },

    async handleDataFeedBack(dataFeedback, toolId, email, phone, fullName) {
      let satisfaction = dataFeedback[0].rating;
      switch (dataFeedback[0].rating) {
        case 1:
          satisfaction = "Rất tệ";
          break;
        case 2:
          satisfaction = "Tệ";
          break;
        case 3:
          satisfaction = "Trung bình";
          break;
        case 4:
          satisfaction = "Tốt";
          break;
        case 5:
          satisfaction = "Rất tốt";
          break;
        default:
          break;
      }
      const tool = await this.broker.call("tools.get", {id: toolId});
      return {
        userEmail: email,
        phone: phone,
        fullName: fullName,
        satisfaction,
        time: this.formatDate(new Date()),
        helpfulness: dataFeedback[1]?.rating,
        upgrade: dataFeedback[2]?.bool ? "Có" : "Không",
        opinion: dataFeedback[3]?.comment,
        feature: tool.localization.name.vi || tool.name,
      }
    },
    formatDate(date) {
      const d = new Date(date);
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const month = String(d.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
      const year = d.getFullYear();

      return `${hours}:${minutes}, ${day}-${month}-${year}`;
    },
    async statisticUserFeedbacks(queryFeedback, queryFilter, paramsPage, keyFilter, lang) {
      let sort = paramsPage.sort;
      if (sort.includes("toolName")) {
        sort += `.${lang}`;
      }

      let sortAggregate = {[sort]: 1};
      if (sort.indexOf('-') !== -1) {
        sortAggregate = {[sort.split('-')[1]]: -1};
      }

      const [result] = await Model.aggregate([
        {
          $match: queryFilter
        },
        {
          $group: {
            _id: "$submitFeedbackId",
            userFeedbacks: {$push: "$$ROOT"},
            createdAt: {$first: "$createdAt"}
          }
        },
        {
          $lookup: {
            from: "SubmitFeedback",
            localField: "_id",
            foreignField: "_id",
            as: "submitFeedback"
          }
        },
        {
          $lookup: {
            from: "Tool",
            localField: "submitFeedback.toolId",
            foreignField: "_id",
            as: "tool"
          }
        },
        {
          $lookup: {
            from: "User",
            localField: "submitFeedback.userId",
            foreignField: "_id",
            as: "user",
          }
        },
        {
          $lookup: {
            from: "GroupFeedback",
            localField: "submitFeedback.groupFeedbackId",
            foreignField: "_id",
            as: "groupFeedback",
          }
        },
        {
          $project: {
            _id: 1,
            toolName: {$first: "$tool.localization.name"},
            projectId: {$first: "$userFeedbacks.projectId"},
            email: {$first: "$user.email"},
            phone: {$first: "$user.phone"},
            feedbacks: {$first: "$groupFeedback.feedbacks"},
            createdAt: {
              $dateToString: {
                date: '$createdAt',
                timezone: '+07:00',
              },
            },
            userFeedbacks: 1,
          }
        },
        {$match: queryFeedback},
        {
          $project: {
            _id: 1,
            toolName: 1,
            projectId: 1,
            email: 1,
            phone: 1,
            createdAt: 1,
            feedbacks: 1,
            userFeedbacks: 1,
            feedback: {
              $map: {
                input: "$feedbacks",
                as: "feedback",
                in: {
                  k: {$concat: ["feedback", {$toString: "$$feedback.index"}]},
                  v: {
                    $let: {
                      vars: {
                        matchedIndex: {$indexOfArray: ["$userFeedbacks.feedbackId", "$$feedback.feedbackId"]},
                        matchedFeedback: {
                          $arrayElemAt: ["$userFeedbacks", {
                            $indexOfArray: ["$userFeedbacks.feedbackId", "$$feedback.feedbackId"]
                          }]
                        }
                      },
                      in: {
                        $cond: {
                          if: {$eq: ["$$matchedIndex", -1]},
                          then: null,
                          else: {
                            $ifNull: [
                              "$$matchedFeedback.bool",
                              "$$matchedFeedback.rating",
                              "$$matchedFeedback.comment"
                            ]
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: [
                {
                  toolName: "$toolName",
                  projectId: "$projectId",
                  email: "$email",
                  phone: "$phone",
                  createdAt: "$createdAt",
                },
                {$arrayToObject: "$feedback"}
              ]
            }
          }
        },
        {
          $match: keyFilter
        },
        //Sắp xếp
        {$sort: sortAggregate},
        // Phân trang
        {
          $facet: {
            rows: [
              {$skip: (paramsPage.page - 1) * paramsPage.pageSize},
              {$limit: paramsPage.pageSize}
            ],
            metadata: [
              {$count: "total"},
              {
                $addFields: {
                  page: paramsPage.page,
                  pageSize: paramsPage.pageSize,
                  totalPages: {$ceil: {$divide: ["$total", paramsPage.pageSize]}}
                }
              }
            ]
          }
        },
        {
          $project: {
            rows: 1,
            total: {$ifNull: [{$arrayElemAt: ["$metadata.total", 0]}, 0]},
            page: {$ifNull: [{$arrayElemAt: ["$metadata.page", 0]}, 0]},
            pageSize: {$ifNull: [{$arrayElemAt: ["$metadata.pageSize", 0]}, 0]},
            totalPages: {$ifNull: [{$arrayElemAt: ["$metadata.totalPages", 0]}, 0]}
          }
        }
      ])
      return result;
    },
    async analysisSubmitFeedback(queryFilter, queryAnalysis, feedbackData, paramsPage, lang) {
      let sort = paramsPage.sort;
      if (sort.includes("toolName")) {
        sort += `.${lang}`;
      }

      let sortAggregate = {[sort]: 1};
      if (sort.indexOf('-') !== -1) {
        sortAggregate = {[sort.split('-')[1]]: -1};
      }

      const [result] = await Model.aggregate([
        {
          $match: queryAnalysis
        },
        {
          $lookup: {
            from: "SubmitFeedback",
            localField: "submitFeedbackId",
            foreignField: "_id",
            as: "submitFeedback"
          }
        },
        {
          $group: {
            _id: {
              toolId: {$first: "$submitFeedback.toolId"},
              feedbackId: "$feedbackId"
            },
            groupFeedbackId: {$first: "$submitFeedback.groupFeedbackId"},
            total: {$sum: 1},
            value: {
              $avg: {
                $switch: {
                  branches: [
                    {case: {$ifNull: ["$rating", false]}, then: {$divide: [{$subtract: ["$rating", 1]}, 4]}},
                    {case: {$ne: [{$ifNull: ["$bool", null]}, null]}, then: {$cond: [{$eq: ["$bool", true]}, 1, 0]}}
                  ],
                  default: null
                }
              }
            }
          }
        },
        {
          $addFields: {
            feedback: {
              $arrayElemAt: [
                {
                  $filter: {
                    input: feedbackData,
                    as: "feedback",
                    cond: {$eq: ["$$feedback.feedbackId", "$_id.feedbackId"]}
                  }
                },
                0
              ]
            }
          }
        },
        {
          $project: {
            toolId: "$_id.toolId",
            groupFeedbackId: {$first: "$groupFeedbackId"},
            feedbackIndex: {$concat: ["feedback", {$toString: "$feedback.index"}]},
            value: 1,
            total: 1
          }
        },
        {
          $group: {
            _id: "$toolId",
            feedbacks: {
              $push: {
                k: "$feedbackIndex",
                v: "$value"
              }
            },
            total: {$max: "$total"},
            groupFeedbackId: {$first: "$groupFeedbackId"}
          }
        },
        {
          $lookup: {
            from: "Tool",
            localField: "_id",
            foreignField: "_id",
            pipeline: [{$project: {"localization.name": 1}}],
            as: "toolId"
          }
        },
        {
          $project: {
            toolName: {$first: "$toolId.localization.name"},
            total: 1,
            feedbacks: 1,
            groupFeedbackId: 1,
          }
        },
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: [
                {
                  toolName: "$toolName",
                  total: "$total",
                  toolId: "$_id",
                  groupFeedbackId: "$groupFeedbackId"
                },
                {$arrayToObject: "$feedbacks"}
              ]
            }
          }
        },
        {
          $match: queryFilter
        },

        {$sort: sortAggregate},
        // Phân trang
        {
          $facet: {
            rows: [
              {$skip: (paramsPage.page - 1) * paramsPage.pageSize},
              {$limit: paramsPage.pageSize}
            ],
            metadata: [
              {$count: "total"},
              {
                $addFields: {
                  page: paramsPage.page,
                  pageSize: paramsPage.pageSize,
                  totalPages: {$ceil: {$divide: ["$total", paramsPage.pageSize]}}
                }
              }
            ]
          }
        },
        {
          $project: {
            rows: 1,
            total: {$ifNull: [{$arrayElemAt: ["$metadata.total", 0]}, 0]},
            page: {$ifNull: [{$arrayElemAt: ["$metadata.page", 0]}, 0]},
            pageSize: {$ifNull: [{$arrayElemAt: ["$metadata.pageSize", 0]}, 0]},
            totalPages: {$ifNull: [{$arrayElemAt: ["$metadata.totalPages", 0]}, 0]}
          }
        }
      ])

      return result;
    }
  },
  events: {}
};
