const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {SUBMIT_FEEDBACK, USER, GROUP_FEEDBACK, TOOL} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    userId: {type: Schema.Types.ObjectId, ref: USER},
    isDeleted: {type: Boolean, default: false},
    toolId: {type: Schema.Types.ObjectId, ref: TOOL},
    groupFeedbackId: {type: Schema.Types.ObjectId, ref: GROUP_FEEDBACK},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(SUBMIT_FEEDBACK, schema, SUBMIT_FEEDBACK);
