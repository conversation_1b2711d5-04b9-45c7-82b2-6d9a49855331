const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./feedback.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_STATE} = require("../../constants/constant");

module.exports = {
  name: 'feedbacks',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: []
  },

  hooks: {},

  actions: {},
  methods: {},
  events: {}
};
