const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {FEEDBACK} = require("../../constants/dbCollections");

const schema = new Schema(
  {
    localization: {
      name: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
      description: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
    },
    subscriptionType: {type: String, enum: ["free", "paid", "system"], default: "system"},
    feedbackType: {type: String, enum: ["icon", "number", "bool", "comment"]},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(FEEDBACK, schema, FEEDBACK);
