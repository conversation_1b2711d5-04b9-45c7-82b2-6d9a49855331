const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./customHeader.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const { USER_CODES } = require("../../../constants/constant");
const AuthRole = require("../../../mixins/authRole.mixin");
const i18next = require("i18next");
const { MoleculerClientError } = require("moleculer").Errors;

module.exports = {
  name: "customheader",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "docxTemplateId": 'docxtemplates.get',
      "userId": 'users.get',
      "organizationId": 'organizations.get',
    },
    populateOptions: ["docxTemplateId", "userId", "organizationId"],
  },

  hooks: {
    before: {
      "makeHeaderOrg": "checkPermission",
    }
  },

  actions: {

    makeDefaultHeader: {
      rest: "POST /makeDefaultHeader",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { docxTemplateId, customHeader } = ctx.params;
        const headerDetail = await this.adapter.findOne({ docxTemplateId, isDefault: true });

        let defaultHeader;
        if (headerDetail) {
          defaultHeader = await this.adapter.updateById(headerDetail._id, { customHeader });
        } else {
          defaultHeader = await this.adapter.insert({ docxTemplateId, customHeader, isDefault: true });
        }

        await ctx.call("customthumbnail.makeDefaultThumbnail", { docxTemplateId, defaultHeader });
        return defaultHeader;
      }
    },

    makeHeaderOrg: {
      rest: "POST /makeHeaderOrg",
      role: USER_CODES.CONTRIBUTOR,
      async handler(ctx) {

        const { docxTemplateId, customHeader, organizationId } = ctx.params;

        const headerDetail = await this.adapter.findOne({ docxTemplateId, organizationId });

        let orgHeader;
        if (headerDetail) {
          orgHeader =  await this.adapter.updateById(headerDetail._id, { customHeader });
        } else {
          orgHeader =  await this.adapter.insert({ docxTemplateId, organizationId, customHeader, isDefault: true });
        }
        await ctx.call("customthumbnail.makeThumbnailOrg", { docxTemplateId, organizationId });
        return orgHeader;
      }
    },
    makeHeaderUser: {
      rest: "POST /makeHeaderUser",
      async handler(ctx) {
        const userId = ctx.meta.user._id;
        const { docxTemplateId, customHeader } = ctx.params;
        const headerDetail = await this.adapter.findOne({ docxTemplateId, userId });

        let userHeader;
        if (headerDetail) {
          userHeader = await this.adapter.updateById(headerDetail._id, { customHeader });
        } else {
          userHeader = await this.adapter.insert({ docxTemplateId, userId, customHeader });
        }

        await ctx.call("customthumbnail.makeThumbnailUser", { docxTemplateId, userId });
        return userHeader;
      }
    },
    getOne: {
      rest: "GET /getOne",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { docxTemplateId, userId, organizationId } = ctx.params;
        return await this.adapter.findOne({ docxTemplateId, userId, organizationId });
      }
    },
    getDefaultHeader: {
      rest: "GET /getDefaultHeader",
      role: USER_CODES.CONTRIBUTOR,
      async handler(ctx) {
        const { docxTemplateId, organizationId } = ctx.params;
        const defaultHeaderOrg = await this.adapter.findOne({ docxTemplateId, organizationId, isDefault: true });
        if (!defaultHeaderOrg) {
          return await this.adapter.findOne({ docxTemplateId, isDefault: true });
        }
        return defaultHeaderOrg;
      }
    },

    getHeaderUserTemplate: {
      rest: "GET /getHeaderUserTemplate",
      async handler(ctx) {
        const { _id: userId, organizationId } = ctx.meta.user;
        const { docxTemplateId } = ctx.params;
        const userCustomHeader = await this.adapter.findOne({ docxTemplateId, userId });
        if (userCustomHeader) {
          return userCustomHeader;
        }
        const defaultHeaderOrg = await this.adapter.findOne({ docxTemplateId, organizationId, isDefault: true });
        if (!defaultHeaderOrg) {
          return await this.adapter.findOne({ docxTemplateId, isDefault: true });
        }
        return defaultHeaderOrg;
      }
    }
  },
  methods: {
    async checkPermission(context) {
      const {params, meta} = context;
      const {user} = meta;
      const {organizationId} = params;

      if (user?.isSystemAdmin) return;

      if (user?.organizationId?.toString() !== organizationId?.toString()) {
        throw new MoleculerClientError(i18next.t("you_need_to_be_an_admin"), 403, "FORBIDDEN");
      }

      const organization = await context.call("organizations.getOne", {id: organizationId});
      if (!organization) {
        throw new MoleculerClientError(i18next.t("error_organization_not_found"), 404);
      }

      if (organization?.active === false) {
        throw new MoleculerClientError(i18next.t("organization_already_locked"), 423);
      }
    }
  },
  events: {},
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
