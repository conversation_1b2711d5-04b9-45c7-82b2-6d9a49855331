const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./customThumbnail.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const carbone = require("carbone");
const { DocxImager } = require("../../Report/DocxImager");
const fs = require("fs");
const path = require("path");
const { MoleculerClientError } = require("moleculer").Errors;
const storageDir = path.join(__dirname, "storage");
const AuthRole = require("../../../mixins/authRole.mixin");

module.exports = {
  name: "customthumbnail",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, FileMixin, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "docxTemplateId": 'docxtemplates.get',
      "userId": 'users.get',
      "organizationId": 'organizations.get',
      "customThumbnailId": 'files.get',
    },
    populateOptions: ["docxTemplateId", "userId", "organizationId", "customThumbnailId"],
  },

  hooks: {},

  actions: {
    makeDefaultThumbnail: {
      async handler(ctx) {
        try {
          const { docxTemplateId, defaultHeader } = ctx.params;
          const docxTemplate = await ctx.call("docxtemplates.get", { id: docxTemplateId });

          const templatePath = await this.broker.call("files.filePath", { id: docxTemplate.templateId });

          const { avatarId, imageId } = defaultHeader.customHeader || {};

          let fileAfterRender;
          if (imageId) {
            const imagePath = await ctx.call("files.filePath", { id: imageId.toString() });
            fileAfterRender = await this.generateDocumentWithImage(ctx, defaultHeader.customHeader, templatePath, imagePath, avatarId);
          } else {
            fileAfterRender = await this.generateDocument(ctx, defaultHeader.customHeader, templatePath);
          }
          const filePath = await this.convertDocxToPDF(fileAfterRender);
          const file = await ctx.call("files.generateThumbnail", { id: docxTemplate.templateThumbnailId, filePath: filePath });
          await ctx.call("docxtemplates.update", { id: docxTemplateId, templateThumbnailId: file._id });
        } catch(err) {
          console.log(err);
        }
      }
    },
    makeThumbnailOrg: {
      rest: "POST /makeThumbnailOrg",
      async handler(ctx) {
        const { docxTemplateId, organizationId } = ctx.params;

        let customThumbnail = await this.adapter.findOne({ docxTemplateId, organizationId });

        if (!customThumbnail) {
          customThumbnail = await this.adapter.insert({ docxTemplateId, organizationId });
        }

        const templatePath = await this.broker.call("docxtemplates.templateFilePath", { id: docxTemplateId });

        const defaultHeader = await ctx.call('customheader.getDefaultHeader', { docxTemplateId, organizationId });
        const { avatarId, imageId } = defaultHeader.customHeader || {};

        let fileAfterRender;
        if (imageId) {
          const imagePath = await ctx.call("files.filePath", { id: imageId.toString() });
          fileAfterRender = await this.generateDocumentWithImage(ctx, defaultHeader.customHeader, templatePath, imagePath, avatarId);
        } else {
          fileAfterRender = await this.generateDocument(ctx, defaultHeader.customHeader, templatePath);
        }

        const filePath = await this.convertDocxToPDF(fileAfterRender);
        const file = await ctx.call("files.generateThumbnail", {id: customThumbnail.customThumbnailId, filePath: filePath,});
        await this.adapter.updateById(customThumbnail._id, { customThumbnailId: file._id });
      }
    },
    makeThumbnailUser: {
      rest: "POST /makeThumbnailUser",
      async handler(ctx) {
        const { docxTemplateId } = ctx.params;
        const userId = ctx.meta.user._id;
        let customThumbnail = await this.adapter.findOne({ docxTemplateId, userId });

        if (!customThumbnail) {
          customThumbnail = await this.adapter.insert({ docxTemplateId, userId });
        }

        const templatePath = await this.broker.call("docxtemplates.templateFilePath", { id: docxTemplateId });

        const defaultHeader = await ctx.call('customheader.getHeaderUserTemplate', { docxTemplateId });
        const { avatarId, imageId } = defaultHeader.customHeader || {};

        let fileAfterRender;
        if (imageId) {
          const imagePath = await ctx.call("files.filePath", { id: imageId.toString() });
          fileAfterRender = await this.generateDocumentWithImage(ctx, defaultHeader.customHeader, templatePath, imagePath, avatarId);
        } else {
          fileAfterRender = await this.generateDocument(ctx, defaultHeader.customHeader, templatePath);
        }

        const filePath = await this.convertDocxToPDF(fileAfterRender);
        const file = await ctx.call("files.generateThumbnail", {id: customThumbnail.customThumbnailId, filePath: filePath,});
        await this.adapter.updateById(customThumbnail._id, { customThumbnailId: file._id });
      }
    },
  },
  methods: {
    async addImageToDocx(resultFilePath, imageFilePath, imageId) {
      let docxImager = new DocxImager();
      await docxImager.load(resultFilePath);
      await docxImager.replaceWithLocalImage(imageFilePath, imageId, 'png');
      await docxImager.save(resultFilePath);
    },

    async save(stream, filename, folder) {
      const dirPath = this.getDirPath(folder, storageDir);
      const filePath = this.getFilePath(filename, dirPath);
      return this.saveToLocalStorage(stream, filePath);
    },
    async generateDocumentWithImage(ctx, data, templateFilePath, imagePath, replaceImageId) {
      const fileAfterGen = await this.generateDocument(ctx, data, templateFilePath);
      await this.addImageToDocx(fileAfterGen, imagePath, replaceImageId);
      return fileAfterGen;
    },
    async generateDocument(ctx, data, templateFilePath, convertToPdf = false) {
      const options = {
        renderPrefix: 'report',
        reportName: "Report",
        timezone: 'Asia/Saigon',
        ...(convertToPdf && { convertTo: 'pdf' })
      };
      return new Promise((resolve, reject) => {
        carbone.render(templateFilePath, data, options, (err, result) => {
          if (err) reject(err);
          resolve(result);
        });
      });
    },

    async convertDocxToPDF(inputFilePath) {
      const fileBuffer = fs.readFileSync(inputFilePath);
      const options = {
        convertTo: 'pdf',
        extension: 'docx',
      };
      const outPDFPath = this.getFilePath(`report_${ this.getUniqueID() }.pdf`, this.getDirPath("preview", storageDir));

      return new Promise((resolve, reject) => {
        carbone.convert(fileBuffer, options, function (err, result) {
          if (err) return reject(err);
          fs.writeFileSync(outPDFPath, result);
          resolve(outPDFPath);
        });
      });
    },
  },
  events: {},
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
