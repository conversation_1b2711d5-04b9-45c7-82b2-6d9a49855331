const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const {
  DOCX_OPTIONS,
  DOCX_TEMPLATES
} = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  docxTemplateId: { type: Schema.Types.ObjectId, ref: DOCX_TEMPLATES },
  fieldName: { type: String },
  fieldLabel: { type: String },
  isImage: { type: Boolean, default: false },
  isDeleted: { type: Boolean, default: false },

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(DOCX_OPTIONS, schema, DOCX_OPTIONS);

