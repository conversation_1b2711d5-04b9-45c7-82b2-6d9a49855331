const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { PUBLISHED_TEMPLATE, DOCX_TEMPLATES, ORGANIZATION, USER } = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  docxTemplateId: { type: Schema.Types.ObjectId, ref: DOCX_TEMPLATES },
  organizationId: { type: Schema.Types.ObjectId, ref: ORGANIZATION },
  publishedBy: { type: Schema.Types.ObjectId, ref: USER },

  isDeleted: { type: Boolean, default: false },

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(PUBLISHED_TEMPLATE, schema, PUBLISHED_TEMPLATE);

