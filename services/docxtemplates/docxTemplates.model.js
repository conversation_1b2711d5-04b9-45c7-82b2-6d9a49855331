const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { DOCX_TEMPLATES, ORGANIZATION, FILE } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  name: { type: String },
  description: { type: String },
  organizationId: { type: Schema.Types.ObjectId, ref: ORGANIZATION },
  templateId: { type: Schema.Types.ObjectId, ref: FILE },
  templateThumbnailId: { type: Schema.Types.ObjectId, ref: FILE },

  isPublic: { type: Boolean, default: false },
  lastUsed: { type: Date },
  isDeleted: { type: Boolean, default: false },

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(DOCX_TEMPLATES, schema, DOCX_TEMPLATES);

