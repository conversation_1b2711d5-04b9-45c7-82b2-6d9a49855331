const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { TOOL, ORGANIZATION, ORG_TOOL } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  organizationId: { type: Schema.Types.ObjectId, required: true, ref: ORGANIZATION },
  toolId: { type: Schema.Types.ObjectId, required: true, ref: TOOL },

  isDeleted: { type: Boolean, default: false },

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.index({ organizationId: 1 });
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(ORG_TOOL, schema, ORG_TOOL);

