const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./orgTools.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");

module.exports = {
  name: "orgTools",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "toolId": 'tools.get',
      "organizationId": 'orgaizations.get',
    },
    populateOptions: ["toolId", "organizationId"]
  },

  hooks: {},

  actions: {
    remove: {
      rest: "DELETE /:id",
      // auth: "required",
      params: {
        id: "string",
      },
      /** @param {Context} ctx */
      async handler(ctx) {
        const { id } = ctx.params;
        const orgTool = await this.adapter.findById(id);
        const dataRes = await this.adapter.removeById(id);
        await ctx.emit('orgTools.deleted', orgTool);
        return dataRes;
      }
    },
    insertMany: {
      rest: "POST /insertMany",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { organizationId, toolIds } = ctx.params;

        const operations = toolIds.map(toolId => ({
          updateOne: {
            filter: { organizationId, toolId },
            update: { $set: { organizationId, toolId, isDeleted: false } },
            upsert: true,
          },
        }));

        ctx.emit('insertManyOrgTools', { organizationId, toolIds });
        return await Model.bulkWrite(operations);
      }
    },

  },
  methods: {
    async seedDB() {
    }
  },
  events: {
    unpublicTool: {
      params: {
        toolId: "string",
      },
      async handler(ctx) {
        const { toolId } = ctx.params;
        await this.adapter.updateMany(
          {
            toolId
          },
          {
            $set: { isDeleted: true }
          }
        );
      }
    }
  },
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
