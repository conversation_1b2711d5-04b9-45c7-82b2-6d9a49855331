'use strict';
const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { VNPT_LOG } = require("../../../../constants/dbCollections");

const schema = new Schema(
  {
    url: { type: String, required: true },
    ip: { type: String },
    /**
     * 1: create payment
     * 2: ipn url
     * 3: check order qr code
     */
    type: { type: Number, required: true },
    is_deleted: { type: Boolean, default: false },
    responseCode: { type: String },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

schema.virtual('params').get(function () {
  const urlParmas = new URLSearchParams(this.url.split('?')[1]);
  return Object.fromEntries(urlParmas.entries());
});

schema.set('toJSON', {
  virtuals: true,
  versionKey: false,
  transform: function (doc, ret) {
    // remove these props when object is serialized
    delete ret._id;
    delete ret.id;
  },
});

module.exports = mongoose.model(VNPT_LOG, schema, VNPT_LOG);

