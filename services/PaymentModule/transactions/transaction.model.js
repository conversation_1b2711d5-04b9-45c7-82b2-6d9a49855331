const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {
  TRANSACTION,
  SUBSCRIPTION,
  CUSTOMER,
  DISCOUNT,
  PACKAGE,
  PROMOTIONS
} = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  subscriptionId: {type: Schema.Types.ObjectId, required: true, ref: SUBSCRIPTION},
  customerId: {type: Schema.Types.ObjectId, required: true, ref: CUSTOMER},
  discountIds: [{type: Schema.Types.ObjectId, ref: DISCOUNT}],
  promotionId: {type: Schema.Types.ObjectId, ref: PROMOTIONS},
  packageId: {type: Schema.Types.ObjectId, ref: PACKAGE},
  content: {type: String},
  paymentCode: {type: String},
  paymentUnit: {type: String},
  unitPrice: {type: String},
  intervalCount: {type: Number},
  state: {
    type: String,
    enum: ["processing", "done", "error"],
  },
  paymentMethod: {
    type: String,
    enum: ["vnpay", "cash", "vnptpay", "vnptqr", "vnpt"],
  },
  cost: {type: String},
  packageQuantity: {type: Number},
  responseCode: {type: String},
  responseMessage: {type: String},
  vnpExpireDate: {type: Date},
  paymentUrl: {type: String},
  isDeleted: {type: Boolean, default: false},

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(TRANSACTION, schema, TRANSACTION);

