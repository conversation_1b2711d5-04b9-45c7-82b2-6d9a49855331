const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./transaction.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");
const i18next = require("i18next");
const {sendEmail} = require("../../../helpers/emailHelper");
const {getConfig} = require("../../../config/config");
const MailMixin = require("../../../mixins/mailSupport.mixin");
const dayjs = require("dayjs");
const config = getConfig(process.env.NODE_ENV);

module.exports = {
  name: "transactions",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole, MailMixin],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      customerId: "customers.get",
      subscriptionId: "subscriptions.get",
      packageId: "packages.get",
      discountIds: "discounts.get",
      promotionId: "promotions.get",
    },
    populateOptions: ["customerId", "subscriptionId.packageId", "discountIds", "promotionId", "packageId"],
  },

  hooks: {},

  actions: {
    paymentHistory: {
      rest: "GET /paymentHistory",
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const customer = await ctx.call("customers.getOneByUser", {userId: user._id});
        return await ctx.call("transactions.find", {query: {customerId: customer._id}, sort: "-createdAt"});
      },
    },
    getPaymentHistoryByUserId: {
      rest: "GET /:userId/paymentHistory",
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {userId} = ctx.params;
        const customer = await ctx.call("customers.getOneByUser", {userId});
        return await ctx.call("transactions.find", {query: {customerId: customer._id}, sort: "-createdAt"});
      },
    },
    testPaymentDone: {
      rest: "POST /testPaymentDone",
      async handler(ctx) {
        const {transactionId} = ctx.params;
        const state = "done";
        const responseCode = "01";
        const responseMessage = "oke";
        const transaction = await this.adapter.updateById(transactionId, {state, responseCode, responseMessage});
        if (state === "done") {
          this.broker.emit("discountsUsed", {
            discountIds: transaction.discountIds,
            customerId: transaction.customerId,
          });
          const transactionTransformed = await this.transformDocuments(
            ctx,
            {populate: this.settings.populateOptions},
            transaction,
          );
          if (
            (transactionTransformed.packageId && transactionTransformed.packageId.type === "base") ||
            transactionTransformed.packageId.customerTarget === "student"
          ) {
            await this.broker.emit("subscriptionUpdateStatus", {
              subscriptionId: transaction.subscriptionId._id.toString(),
              customer: transactionTransformed.customerId,
            });
          } else {
            await this.broker.emit("addOnPackageOrdered", {
              packages: transactionTransformed.packageId,
              customer: transactionTransformed.customerId,
            });
          }
        }

        this.broker.emit(`sse.${transactionId}`, transaction);
      },
    },
    getAllWithPagination: {
      rest: "GET /getAll",
      async handler(ctx) {

        const {query: queryString = "{}", sort, populate} = ctx.params;
        const query = {...JSON.parse(queryString)};
        if (query.time) {
          const {createdAt} = this.extractQueryTime(query);
          query.createdAt = createdAt;
          delete query.time;
          delete query.fromDate;
          delete query.toDate;
        }
        const params = this.constructParams(ctx.params, query, sort);

        return await ctx.call("transactions.list", {...params, populate});

      },

    },

    /**
     * Kiểm tra và cập nhật các transaction VNPay đã quá hạn
     * Chạy bởi cronjob mỗi giờ
     */
    checkAndUpdateExpiredVnpayTransactions: {
      async handler(ctx) {
        try {
          // Tính thời gian 30 phút trước
          const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

          // Tìm các transaction VNPay đang processing và được tạo trước 1 giờ
          const expiredTransactions = await this.adapter.find({
            query: {
              paymentMethod: "vnpay",
              state: "processing",
              createdAt: {$lt: oneHourAgo},
              isDeleted: {$ne: true}
            }
          });

          if (expiredTransactions.length === 0) {
            console.log('Không có transaction VNPay nào quá hạn');
            return {
              message: 'Không có transaction VNPay nào quá hạn',
              count: 0
            };
          }

          // Cập nhật state thành error cho các transaction quá hạn
          const updatePromises = expiredTransactions.map(transaction =>
            this.adapter.updateById(transaction._id, {
              state: "error",
              responseCode: "TIMEOUT",
              responseMessage: "Transaction VNPay quá hạn"
            })
          );

          await Promise.all(updatePromises);

          console.log(`Đã cập nhật ${expiredTransactions.length} transaction VNPay quá hạn thành error`);

          // // Emit event để thông báo qua Telegram nếu cần
          // if (expiredTransactions.length > 0) {
          //   this.broker.emit('vnpayTransactionTimeout', {
          //     count: expiredTransactions.length,
          //     transactions: expiredTransactions.map(t => ({
          //       id: t._id,
          //       customerId: t.customerId,
          //       cost: t.cost,
          //       createdAt: t.createdAt
          //     }))
          //   });
          // }

          return {
            message: `Đã cập nhật ${expiredTransactions.length} transaction VNPay quá hạn`,
            count: expiredTransactions.length,
            transactionIds: expiredTransactions.map(t => t._id)
          };

        } catch (error) {
          console.error('Lỗi khi kiểm tra transaction VNPay quá hạn:', error);
          throw error;
        }
      }
    }


  },
  methods: {
    constructParams(params, query, sort) {
      return {
        ...this.extractParamsList(params),
        query: JSON.stringify(query),
        sort,
        populate: [],
      };
    },

    async sendTransactionEmail(transaction) {
      const dataSendmail = {
        userName: transaction.customerId.fullName || transaction.customerId.email || "",
        packageName: transaction.packageId.name || "",
        paymentDate: dayjs(transaction.createdAt).format('HH:mm DD/MM/YYYY'), // format to "hh:mm DD/MM/YYYY"
        paymentMethod: transaction.paymentMethod,
        totalAmount: transaction.cost,
      }
      const formHtml = this.createTransactionEmail(dataSendmail);
      let mailOptions = {
        from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
        to: transaction.customerId.email, // list of receivers
        subject: 'Thanh toán', // Subject line
        html: formHtml,
      };
      sendEmail(mailOptions, (err) => {
        if (err) {
          console.log(err);
        }
      });

      let options = {
        from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
        to: config.mail.auth.user, // list of receivers
        subject: 'Thanh toán', // Subject line
        html: formHtml,
      };
      sendEmail(mailOptions, (err) => {
        if (err) {
          console.log(err);
        }
      });
      this.broker.emit("mail.sent", {
        to: transaction.customerId.email,
        subject: 'Thanh toán',
        status: "success",
        data: dataSendmail,
      });
    },
  },
  events: {
    listenCreateTransaction: {
      params: {
        transactionId: "string",
      },
      async handler(context) {
        const {transactionId} = context.params;
        const transaction = await this.adapter.findById(transactionId);
        context.emit(`sse.${transactionId}`, transaction);
      },
    },
    transactionUpdateState: {
      params: {
        state: "string",
        transactionId: "string",
        // responseCode: "string",
        // responseMessage: "string",
      },
      async handler(context) {
        const {state, transactionId, responseCode, responseMessage} = context.params;
        const transaction = await this.adapter.updateById(transactionId, {state, responseCode, responseMessage});
        if (state === "done") {
          this.broker.emit("discountsUsed", {
            discountIds: transaction.discountIds,
            customerId: transaction.customerId,
          });
          const transactionTransformed = await this.transformDocuments(
            context,
            {populate: this.settings.populateOptions},
            transaction,
          );
          if (
            (transactionTransformed.packageId && transactionTransformed.packageId.type === "base") ||
            transactionTransformed.packageId.customerTarget === "student"
          ) {
            console.log("transactionTransformed", transactionTransformed);
            await this.broker.emit("subscriptionUpdateStatus", {
              subscriptionId: transaction.subscriptionId._id.toString(),
              customer: transactionTransformed.customerId,
            });
          } else {
            await this.broker.emit("addOnPackageOrdered", {
              packages: transactionTransformed.packageId,
              customer: transactionTransformed.customerId,
            });
          }
          this.sendTransactionEmail(transactionTransformed);

        }

        this.broker.emit(`sse.${transactionId}`, transaction);
      },
    },

    vnptTransactionUpdateState: {
      params: {
        state: "string",
        transactionId: "string",
        // responseCode: "string",
        // responseMessage: "string",
      },
      async handler(context) {
        const {state, transactionId, responseCode, responseMessage} = context.params;
        const transaction = await this.adapter.updateById(transactionId, {state, responseCode, responseMessage});
        if (state === "done") {
          this.broker.emit("discountsUsed", {
            discountIds: transaction.discountIds,
            customerId: transaction.customerId,
          });
          const transactionTransformed = await this.transformDocuments(
            context,
            {populate: this.settings.populateOptions},
            transaction,
          );

          await this.broker.emit("vnptSubscriptionUpdateStatus", {
            subscriptionId: transaction.subscriptionId._id.toString(),
            customer: transactionTransformed.customerId,
          });

          this.sendTransactionEmail(transactionTransformed);

        }

        this.broker.emit(`sse.${transactionId}`, transaction);
      },
    },
  },
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
