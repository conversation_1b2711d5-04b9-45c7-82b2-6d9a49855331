const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {SUPPORT_BUSINESS} = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  fullName: {type: String, trim: true},
  email: {type: String, trim: true, lowercase: true, required: "Please fill in an email"},
  phone: {type: String},
  company: {type: String},

  hasSupport: {type: Boolean, default: false},
  content: {type: String},

  isDeleted: {type: Boolean, default: false},

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(SUPPORT_BUSINESS, schema, SUPPORT_BUSINESS);

