const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./datasets.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");

module.exports = {
  name: 'datasets',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "instructionId": 'instructions.get'
    },
    populateOptions: ["instructionId"]
  },

  hooks: {
    before: {}
  },

  actions: {
    remove: {
      rest: "DELETE /:id",
      params: {
        id: "string",
      },
      /** @param {Context} ctx */
      async handler(ctx) {
        const { id } = ctx.params;
        const update = { isDeleted: true };
        const options = { new: true };

        const updatedData = await this.adapter.updateById(id, update, options);
        await ctx.emit('datasets.deleted', id);

        return updatedData;
      }
    },
    findOne: {
      rest: "GET",
      async handler(ctx) {
        const { query } = ctx.params;
        return await this.adapter.findOne(query);
      }
    }
  },
  methods: {
    async seedDB() {
    },
  },
  events: {},
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
