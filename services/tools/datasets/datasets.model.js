const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { DATASET, INSTRUCTION } = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    instructionId: { type: Schema.Types.ObjectId, ref: INSTRUCTION },
    name: { type: String, required: true },
    isDefault: { type: Boolean, default: false },

    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(DATASET, schema, DATASET);
