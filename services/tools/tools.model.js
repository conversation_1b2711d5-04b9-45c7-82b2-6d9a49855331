const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {TOOL, INSTRUCTION, ORGANIZATION, TOOL_GROUP} = require("../../constants/dbCollections");
const {PERSONA} = require("../../constants/constant");

const toolSchema = new Schema(
  {
    toolId: {type: String, validate: /\S+/},
    name: {type: Schema.Types.Mixed, required: true},
    shortName: {type: String, validate: /\S+/},
    displayName: {type: Schema.Types.Mixed},
    urlName: {type: String, validate: /\S+/},
    inputType: {type: String, required: true, validate: /\S+/},
    maxInputLength: {type: Number, default: 1000},
    outputType: {type: String, validate: /\S+/},
    description: {type: String},
    categories: {type: String, validate: /\S+/},
    isFavorite: {type: Boolean, default: false},
    inputPlaceholder: {type: String},
    inputLabel: {type: String},
    isOrganizationTool: {type: Boolean, default: false},
    showOnWelcome: [
      {
        type: String,
        enum: Object.values(PERSONA), default: "other"
      }
    ],
    instruction: {type: String},
    gptModel: String,
    visible: {
      type: String,
      enum: ["public", "developing", "private"],
      default: "developing",
    },
    instructionIds: [{type: Schema.Types.ObjectId, ref: INSTRUCTION}],
    organizationIds: [{type: Schema.Types.ObjectId, ref: ORGANIZATION}],
    groupToolIds: [{type: Schema.Types.ObjectId, ref: TOOL_GROUP}],
    contentTitle: {type: String},

    linkYoutube: {type: String},
    type: {
      type: String,
      enum: ["EXAM_SCHOOL", "EXAM_IELTS", "MARK_TEST_SCHOOL", "MARK_TEST_IELTS", "NORMAL"],
      default: "NORMAL"
    },
    showPreviewInput: {type: Boolean, default: false},
    localization: {
      name: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
      description: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
      contentTitle: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
      categories: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
      inputPlaceholder: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
      inputLabel: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
    },

    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(TOOL, toolSchema, TOOL);
