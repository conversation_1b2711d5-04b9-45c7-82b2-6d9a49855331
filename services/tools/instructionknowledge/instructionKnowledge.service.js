const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./instructionKnowledge.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");

module.exports = {
  name: 'instructionKnowledge',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "instructionId": 'instructions.get',
      "knowledgeId": 'knowledge.get',
    },
    populateOptions: ["knowledgeId"]
  },

  hooks: {
    before: {}
  },

  actions: {},
  methods: {},
  events: {
    async updateKnowledge(data) {
      const { knowledges, instructionId } = data;
      await this.adapter.updateMany(
        {
          instructionId,
          knowledgeId: { $nin: knowledges }
        },
        {
          $set: { isDeleted: true }
        }
      );
      const bulkWriteOperations = knowledges.map(knowledgeId => ({
        updateOne: {
          filter: { knowledgeId , instructionId },
          update: { $set: { knowledgeId , instructionId, isDeleted: false } },
          upsert: true,
        },
      }));
      await Model.bulkWrite(bulkWriteOperations);
    },
    async knowledgeDeleted(knowledgeId) {
      await this.adapter.updateMany(
        { knowledgeId },
        { isDeleted: true }
      );
    }
  },

};
