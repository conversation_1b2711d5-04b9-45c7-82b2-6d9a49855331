const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { INSTRUCTION_KNOWLEDGE, INSTRUCTION, KNOWLEDGE } = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    instructionId: { type: Schema.Types.ObjectId, ref: INSTRUCTION },
    knowledgeId: { type: Schema.Types.ObjectId, ref: KNOWLEDGE },

    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(INSTRUCTION_KNOWLEDGE, schema, INSTRUCTION_KNOWLEDGE);
