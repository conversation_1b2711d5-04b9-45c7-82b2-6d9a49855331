[{"toolId": "transcriptYoutube", "name": "YouTube Video To Text", "shortName": "Video Transcript", "inputType": "video", "outputType": "text", "urlName": "video-to-text", "description": "Creates a script to the chosen Youtube video that you can use in class.", "categories": " Listening", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd088"], "instruction": "Creates a corrected text with dots, commas, and question marks"}, {"toolId": "transcriptAudio", "name": "Audio To Text", "shortName": "Video Transcript", "inputType": "audio", "outputType": "text", "urlName": "audio-to-text", "description": "Creates a script to the chosen audio that you can use in class.", "categories": " Listening", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd089"], "instruction": "Creates a corrected text with dots, commas, and question marks"}, {"toolId": "createQuestionsForVideo", "name": "Create questions for a YouTube video", "shortName": "YouTube video with questions", "urlName": "video-to-questions", "inputType": "video", "outputType": "questions", "description": "Creates a list of open questions, multiple choice questions, or true/false statements for a YouTube video. You can use it to create a listening comprehension exercise.", "categories": " Listening", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd08a", "658543045461b66550acd0a3", "658543045461b66550acd0a2", "658543045461b66550acd08e"]}, {"toolId": "createQuestionsForAudio", "name": "Create questions for a audio", "shortName": "Audio with questions", "urlName": "audio-to-questions", "inputType": "audio", "outputType": "questions", "description": "Creates a list of open questions, multiple choice questions, or true/false statements for a Audio. You can use it to create a listening comprehension exercise.", "categories": " Listening", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd08a", "658543045461b66550acd0a3", "658543045461b66550acd0a2", "658543045461b66550acd08e"]}, {"toolId": "createOpenQuestionsForAudio", "name": "Create open questions for a audio", "shortName": "Open questions for a audio", "urlName": "audio-to-questions", "inputType": "audio", "outputType": "questions", "description": "Creates a list of open questions, multiple choice questions, or true/false statements for a YouTube video. You can use it to create a listening comprehension exercise.", "categories": " Listening", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd08a"], "instruction": "Creates exactly ten open questions for the text for the student to check if they understand the content of the text.."}, {"toolId": "createSummaryOptions", "name": "Summaries for a YouTube video", "shortName": "YouTube summaries", "urlName": "youtube-summaries", "inputType": "video", "outputType": "summaries", "description": "Creates three summaries for a YouTube video, two of them are wrong and only one is correct. You can give this task to check listening comprehension.", "categories": "Listening", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd08b"], "instruction": "Please creates exactly three summaries for this input text, two of them are wrong and only one is correct."}, {"toolId": "createWarmDiscussionQuestionsForVideo", "name": "Create warm-up discussion questions for a video", "shortName": "Discussion questions for a video", "urlName": "video-discussion-questions", "inputType": "video", "outputType": "questions", "description": "Creates discussion questions that you can use as a warm-up before watching a video. They invite your student to express their opinion on the topic covered in the video and talk about their experience.", "categories": "Speaking", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd08c"], "instruction": "Creates discussion questions that teacher can use as a warm-up before reading text.\n    The question invite your student to express their opinion on the topic covered in the text and talk about their experience.\n    Let analytic the topic of the text, and create open question for the topic"}, {"toolId": "createTextOnTopic", "name": "Create a text on a certain topic", "shortName": "Create a text", "urlName": "create-text", "inputType": "topic", "outputType": "text", "description": "Creates a text on a certain topic. Can be used during a lesson or as a homework task after a reading, listening or vocabulary lesson.", "categories": " Reading", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd08d"], "instruction": "Creates a text on a certain topic.\n    Can be used during a lesson or as a homework task after a reading, listening or vocabulary lesson."}, {"toolId": "createOpenQuestionsForText", "name": "Create open questions to the text", "shortName": "Open questions", "urlName": "open-questions", "inputType": "text", "outputType": "questions", "description": "Creates a list of open questions. Ask your students these questions to make sure they understand the text.", "categories": " Reading", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd08a"], "instruction": "Creates exactly ten open questions for the text for the student to check if they understand the content of the text.."}, {"toolId": "createABCDQuestionsForText", "name": "Create ABCD questions for a text with only one correct answer", "shortName": "ABCD questions with only one correct answer", "urlName": "abcd-questions", "outputType": "questions", "inputType": "text", "description": "Creates a list of ABCD questions for a text with only one correct answer.", "categories": " Reading", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd08e"], "instruction": "Creates exactly ten ABCD questions for this text for the student to check if they understand the content of the text.\n    Each question has four options and only one correct answer."}, {"toolId": "CreateTrueFalseQuestionsOnText", "name": "Create True/False statements based on your text", "shortName": "True / False", "urlName": "true-false", "inputType": "text", "outputType": "questions", "description": "Creates a true/false reading exercise for the student to check if they understand the content of the text.", "categories": " Reading", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd0a2"]}, {"toolId": "createDialogueOnTopic", "name": "Create a dialogue on any topic", "shortName": "Dialogue based on topic", "urlName": "dialogue-on-topic", "inputType": "topic", "outputType": "dialogue", "description": "Creates a dialogue based on any text or topic. Give this dialogue to your students to read aloud or discuss!", "categories": " Reading, Speaking", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createLeadInActivitiesForText", "name": "Lead-in activities for a text", "shortName": "Lead-in activities", "urlName": "lead-in-activities", "inputType": "text", "outputType": "ideas", "description": "Creates 3 ideas that can be used as lead-in activities before reading a certain text in class. Replace simple discussion questions with these to make your lesson more engaging!", "categories": " Speaking", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createThreeTitlesForText", "name": "Create three titles for a text", "shortName": "Titles for a text", "urlName": "titles-for-text", "inputType": "text", "outputType": "titles", "description": "Creates three titles for a given text, two of them are wrong and only one is correct. You can give this exercise to your students as a skimming task.", "categories": " Reading", "isFavorite": true, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd08f"], "instruction": "Creates three titles for a given text, two of them are wrong and only one is correct.\n    You can give this exercise to your students as a skimming task."}, {"toolId": "createSmallTextsForReading", "name": "Reading bits and pieces", "shortName": "Reading bits", "urlName": "reading-bits", "inputType": "text", "outputType": "texts", "description": "Creates all kinds of small texts for your lesson.", "categories": " Reading", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createMatchingExerciseWord", "name": "Create a matching exercise word-definition", "shortName": "Word definition matching", "urlName": "word-definition-matching", "inputType": "words", "outputType": "exercise", "description": "Automatically creates a matching exercise for a list of words. Ask your student to match the word to its meaning!\r\nTip: for words with multiple meanings, provide a synonym in brackets to get the definition you need.", "categories": " Vocabulary", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createSentencesWithTargetVocabulary", "name": "Create a list of sentences with your vocabulary", "shortName": "Sentences with target vocabulary", "urlName": "sentences-with-vocabulary", "inputType": "vocabulary", "outputType": "sentences", "description": "Creates a list of sentences with target vocabulary. It's a great idea to use them later in the \"Fill in the Gap\" tool! Tip: you can type in verb forms or tense form markers to make a grammar task.", "categories": " Vocabulary", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createEssentialVocabulary", "name": "Essential vocabulary on the topic", "shortName": "Essential vocabulary", "urlName": "essential-vocabulary", "inputType": "topic", "outputType": "vocabulary", "description": "Suggests a list of essential vocabulary items for the chosen topic and level. Use this list in the other Twee tools to create a variety of exercises for your lesson.", "categories": " Vocabulary", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "options": ["partOfSpeech"]}, {"toolId": "createWordFormationExercise", "name": "Create a word formation exercise", "shortName": "Word formation", "urlName": "word-formation", "inputType": "words", "outputType": "exercise", "description": "Creates a word formation exercise. Ask your student to take a word from each set of brackets and form a different part of speech to fill in the gaps.", "categories": " Vocabulary", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createCommunicativeSituations", "name": "Create communicative situations with your vocabulary", "shortName": "Communicative situations with your vocabulary", "urlName": "communicative-situations", "inputType": "vocabulary", "outputType": "dialogues", "description": "Creates short two-line dialogues with the target vocabulary. You can give them to your students to show how to use new words and phrases in everyday speech.", "categories": "Vocabulary", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "options": ["level", "targetVocabulary"]}, {"toolId": "extractCollocations", "name": "Extract collocations from a text", "shortName": "Extract collocations", "urlName": "extract-collocations", "inputType": "text", "outputType": "collocations", "description": "Extracts collocations from a given text based for the target vocabulary provided. Perfect for teaching your students vocabulary chunks.", "categories": "Vocabulary", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "options": [], "instruction": "Extracts collocations from a given text based for the target vocabulary provided"}, {"toolId": "createWordTranslationMatching", "name": "Word-Translation matching", "shortName": "Word-Translation matching", "urlName": "word-translation-matching", "inputType": "words", "outputType": "exercise", "description": "This tool translates a list of words or phrases into different world languages and creates a matching exercise. Use it for vocabulary presentation and revision in bilingual classrooms.", "categories": null, "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106", "options": [], "instruction": "Translates a list of words or phrases into Vietnamese world languages and creates a matching exercise.\n    The output should be two lists: the input words (must mixed order), and the translations in Vietnamese."}, {"toolId": "scrambleWords", "name": "Scramble the words in sentences", "shortName": "Scramble the words", "urlName": "scramble-words", "inputType": "words", "outputType": "sentence", "description": "Mixes up all the words in a sentence. Ask your student to put the words in the right order. You can give this task to practice word order in questions or grammar structures.", "categories": " Grammar", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "openBracketVerb", "name": "Open-the-brackets exercise for verbs", "shortName": "Open-the-brackets (verbs)", "urlName": "open-brackets-verbs", "inputType": "words", "outputType": "exercise", "description": "Creates an exercise in which a student needs to open the brackets and fill in the gaps with the correct verb forms. It’s great to use after presenting a new tense and for drilling, especially at lower levels.", "categories": " Grammar", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createMatchingHalves", "name": "Create an exercise with matching halves", "shortName": "Matching halves", "urlName": "halves", "inputType": "text", "outputType": "exercise", "description": "Creates an exercise for your students in which they need to correctly match the halves of collocations, sentences, and so on. It's an excellent way to practise or revise grammar and vocabulary chunks.", "categories": "<PERSON><PERSON>abular<PERSON>, Grammar", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "findDiscussionQuestions", "name": "Find discussion questions", "shortName": "Discussion questions", "urlName": "discussion-questions", "inputType": "topic", "outputType": "questions", "description": "Creates discussion questions on a given topic. You can use them as a warm-up or at any other stage of your lesson.", "categories": " Speaking", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "findInterestingFacts", "name": "Find interesting facts on a given topic", "shortName": "Find facts", "urlName": "find-facts", "inputType": "topic", "outputType": "facts", "description": "Finds 10 facts on a given topic. You can use these facts as an icebreaker, true/false quiz, or as topics for discussion.", "categories": " Speaking", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createListOfAdvantagesAndDisadvantages", "name": "Create a list of advantages and disadvantages on the given topic", "shortName": "Advantages and disadvantages", "urlName": "advantages-disadvantages", "inputType": "topic", "outputType": "list", "description": "Creates a list of advantages and disadvantages on the given topic. You can use this list for discussions or debates.", "categories": " Speaking", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "findQuotesByFamousPeople", "name": "Find quotes by famous people on the topic", "shortName": "Famous quotes", "urlName": "famous-quotes", "inputType": "topic", "outputType": "quotes", "description": "Creates a list of quotes by famous people. Perfect for warm-ups, discussions, and essay topics", "categories": " Speaking, Writing", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createFourOpinions", "name": "Four opinions", "shortName": "Four opinions", "urlName": "four-opinions", "inputType": "topic", "outputType": "opinions", "description": "Creates four opinions by random people on the topic. Use it to encourage students to consider different points of view and evaluate arguments.", "categories": " Speaking, Writing", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "linkWordsIntoSentences", "name": "Link words into sentences", "shortName": "Link words into sentences", "urlName": "link-words-sentences", "inputType": "words", "outputType": "sentences", "description": "Creates an exercise in which a student is expected to come up with unique sentences by logically connecting the words given in each prompt. Can be used as a writing or a speaking exercise.", "categories": "Speaking, Writing", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createListOfEssayTopic", "name": "Create a list of essay topics on a given subject", "shortName": "Essay topics", "urlName": "essay-topics", "inputType": "subject", "outputType": "topics", "description": "Create a list of topics on a given subject. An excellent way to make a writing task for your students. Can be given as homework.", "categories": " Writing", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"toolId": "createWritingWithTargetVocabulary", "name": "Creative writing with target vocabulary", "shortName": "Creative writing", "urlName": "creative-writing", "inputType": "vocabulary", "outputType": "tasks", "description": "Gives a set of creative writing tasks to encourage your students to use the new vocabulary. These include social media posts, comments, emails, reviews and so on.", "categories": "Writing", "isFavorite": false, "hasThumbnail": false, "gptModel": "gpt-3.5-turbo-1106"}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createPictureDescription", "name": "Create a Picture Description", "shortName": "Create a Picture Description", "urlName": "create-text", "inputType": "text", "outputType": "text", "description": "Create a Picture Description. Can be used during a test for Pre A1 Starter.", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd090"], "instruction": "Creates a description of an image about a certain topic. Each person will have the name in the parenthesis. The description should be about the topic and use simple words."}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createConversationAboutPicture", "name": "Create a Conversation About Picture", "shortName": "Create a Conversation About Picture", "urlName": "create-text", "inputType": "text", "outputType": "text", "description": "Create a Conversation About Picture. Can be used during a test for Pre A1 Starter.", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd091"], "instruction": "Creates a conversation script about an image. In the conversation. People will describe the image and use simple words. The script can be use to create listening test for Cambridge English Starters"}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createConversationAboutText", "name": "Create a Conversation About Text", "shortName": "Conversation About Text", "inputType": "text", "outputType": "text", "description": "Create a Conversation About Text.", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd092"], "instruction": "Creates a conversation script base on input text"}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createLeadInActivityIdeas", "name": "Create Lead In Activity Ideas", "shortName": "Lead In Activity Ideas", "inputType": "text", "outputType": "three_ideas", "description": "Creates three ideas that can be used as lead-in activities before reading a certain text in class.", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd093"], "instruction": "Creates three ideas that can be used as lead-in activities before reading a certain text in class."}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createReadingBitsAndPieces", "name": "Create Reading Bits And Pieces", "shortName": "Reading Bits And Pieces", "inputType": "text", "outputType": "text", "description": "Creates ten small texts for your lesson follow the input text and words.", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd094"], "instruction": "Creates all kinds of small texts for your lesson."}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createFillInGapsOfText", "name": "Create Fill In Gaps Of Text", "shortName": "Fill In Gaps Of Text", "inputType": "text", "outputType": "text", "description": "Create Fill In Gaps Of Text.", "categories": " Reading", "gptModel": "gpt-4-1106-preview", "instructionIds": ["658543045461b66550acd095"], "instruction": "Choose ten position in the text and replace its with underline blank \"______\". Each blank should be replaced with one word and have a index number like \"___(1)___\". The output should be have the correct word in the blank position and index number"}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createSentencesFromWords", "name": "Create Sentences From Words", "shortName": "Sentences From Words", "inputType": "text", "outputType": "text", "description": "Create Sentences From Words.", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd096"], "instruction": "Create exactly ten sentences from the words"}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createEssentialVocabulary", "name": "Create Essential Vocabulary", "shortName": "Essential Vocabulary", "inputType": "topic", "outputType": "text", "description": "Create Essential Vocabulary for topic", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd097"], "instruction": "Create essential vocabulary on the topic. Each word seperated by comma. Minimum 10 words. Maximum 20 words."}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createCommunicativeSituations", "name": "Communicative Situations", "shortName": "Communicative Situations", "inputType": "topic", "outputType": "text", "description": "Create Communicative Situations for topic", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd098"], "instruction": "Creates short two-line dialogues with the target vocabulary. You can give them to your students to show how to use new words and phrases in everyday speech."}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createWordTranslationMatching", "name": "Word Translation Matching", "shortName": "Word Translation Matching", "inputType": "text", "outputType": "text", "description": "Translates a list of words or phrases into Vietnamese world languages and creates a matching exercise. The out put should be pairs of word and translation.", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd099"]}, {"isFavorite": true, "hasThumbnail": false, "toolId": "scrambleWordsInSentences", "name": "Scramble Words In Sentences", "shortName": "Mixes up all the words in sentence. The output should be list word of each sentence.", "inputType": "text", "outputType": "text", "description": "", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd09a"], "instruction": "Mixes up all the words in sentence. Ask your student to put the words in the right order. You can give this task to practice word order in questions or grammar structures.\n    Each mixed sentence has a correct answer is complete sentence."}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createDiscussionQuestionsForTopic", "name": "Create Discussion Questions For Topic", "shortName": "Discussion Questions For Topic", "inputType": "topic", "outputType": "text", "description": "Creates discussion questions on a given topic", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd09b"], "instruction": "Creates discussion questions on a given topic"}, {"isFavorite": true, "hasThumbnail": false, "toolId": "findFactAboutTopic", "name": "Find Fact About Topic", "shortName": "Fact About Topic", "inputType": "topic", "outputType": "text", "description": "Finds ten facts on a given topic", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd09c"], "instruction": "Finds ten facts on a given topic"}, {"isFavorite": true, "hasThumbnail": false, "toolId": "advantagesAndDisadvantages", "name": "Advantages And Disadvantages Of Topic", "shortName": "Advantages And Disadvantages", "inputType": "topic", "outputType": "text", "description": "Finds list of advantages and disadvantages on a given topic", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd09d"], "instruction": "Finds list of advantages and disadvantages on a given topic"}, {"isFavorite": true, "hasThumbnail": false, "toolId": "findQuotesAboutTopic", "name": "Find Quotes About Topic", "shortName": "Quotes About Topic", "inputType": "topic", "outputType": "text", "description": "Creates a list of quotes by famous people", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd09e"], "instruction": "Creates a list of quotes by famous people. Perfect for warm-ups, discussions, and essay topics."}, {"isFavorite": true, "hasThumbnail": false, "toolId": "fourOpinionsAboutTopic", "name": "Four Opinions About Topic", "shortName": "Opinions About Topic", "inputType": "topic", "outputType": "text", "description": "Creates four opinions by random people on the topic", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd09f"], "instruction": "Creates four opinions by random people on the topic"}, {"isFavorite": true, "hasThumbnail": false, "toolId": "listEssayTopics", "name": "List Essay Topics", "shortName": "Essay Topics", "inputType": "topic", "outputType": "text", "description": "Create a list of essay topics on a given subject.", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd0a0"], "instruction": "Create a list of essay topics on a given subject. The output is two lists of essay topics: personal and general."}, {"isFavorite": true, "hasThumbnail": false, "toolId": "writingTaskWithTarget", "name": "Create Writing Task With Target", "shortName": "Writing Task With Target", "inputType": "topic", "outputType": "text", "description": "Create a writing task with target.", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd0a1"], "instruction": "Gives a set of creative writing tasks to encourage your students to use the new vocabulary. These include social media posts, comments, emails, reviews and so on."}, {"isFavorite": true, "hasThumbnail": false, "toolId": "fullOptionsTool", "name": "Assistants Full Options", "shortName": "Assistants", "inputType": "text", "outputType": "text", "description": "Assistants Full Options", "categories": " Reading, Writing, Speaking, Listening", "gptModel": "gpt-3.5-turbo-1106"}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createTrueFalseQuestionsForText", "name": "Create True/False statements based on your text", "shortName": "True/False statements based on your text", "inputType": "text", "outputType": "text", "description": "Create True/False statements based on your text", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd0a2"], "instruction": "Creates exactly ten True/False statements for this text for the student to check if they understand the content of the text."}, {"isFavorite": true, "hasThumbnail": false, "toolId": "createABCDQuestionsFillInBlankForText", "name": "Create ABCD questions fill in blank for a text with only one correct answer", "shortName": "ABCD questions fill in blank with only one correct answer", "inputType": "text", "outputType": "text", "description": "Create ABCD questions fill in blank for a text with only one correct answer", "categories": " Reading", "gptModel": "gpt-3.5-turbo-1106", "instructionIds": ["658543045461b66550acd0a3"], "instruction": "Creates exactly ten ABCD questions for this text for the student to check if they understand the content of the text.\n    The question type is fill in the blank, in the each question the blank will represent by ________. The question must be one sentence.\n    Each question has four options and only one correct answer matching with the blank."}]