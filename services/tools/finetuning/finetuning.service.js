const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./finetuning.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const { OpenAI } = require("openai");
const path = require("path");
const fs = require("fs");
const configuration = {
  apiKey:
    process.env.OPENAI_API_KEY ||
    "***************************************************",
};
const storageDir = path.join(__dirname, "storage");
const { MoleculerClientError } = require("moleculer").Errors;

module.exports = {
  name: 'finetuning',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, FileMixin],
  dependencies: ["settings"],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "datasetId": 'datasets.get',
    },
    populateOptions: [
      "datasetId",
    ],
  },

  hooks: {
    before: {
      "*": "getAPIKey",
    }
  },

  actions: {
    createFineTuning: {
      rest: {
        method: "POST",
        path: "/fineTuning",
      },
      // visibility: "protected",
      async handler(ctx) {
        try {
          const { apiKey } = ctx.meta;
          const openai = new OpenAI({ apiKey });

          const { datasetId, instructionId, gptModel } = ctx.params;

          const conversations = await ctx.call("conversations.find", {
            query: { datasetId, isDeleted: false, approved: true },
          });
          if (conversations.length < 10) {
            return new MoleculerClientError("Dataset must have at least 10 conversations approved", 402);
          }

          const messages = conversations.map((conversation) => {
            conversation.messages.map((message) => {
              if (typeof message.content === 'object') {
                message.content = JSON.stringify(message.content);
              }
              return message;
            });
            return { messages: conversation.messages };
          });

          if (messages.length < 10) {
            return new MoleculerClientError("Dataset must have at least 10 messages", 402);
          }
          const sampleFile = await this.createJsonl(messages);
          const traningFile = await openai.files.create({
            file: fs.createReadStream(sampleFile),
            purpose: 'fine-tune'
          });

          const jobFineTuning = await openai.fineTuning.jobs.create({
            training_file: traningFile.id,
            model: gptModel,
          });

          return await this.adapter.insert({
            datasetId,
            instructionId,
            gptModel,
            status: jobFineTuning.status,
            job: jobFineTuning
          });
        } catch (error) {
          throw new MoleculerClientError(error.message, 402);
        }
      }
    },
    getTuning: {
      rest: {
        method: "GET",
        path: "/getTuning",
      },
      async handler(ctx) {
        const { ftjobId } = ctx.params;
        const { apiKey } = ctx.meta;
        const openai = new OpenAI({ apiKey });
        return await openai.fineTuning.jobs.retrieve(ftjobId);
      }
    },
    updateStatus: {
      async handler(ctx) {
        const { apiKey } = ctx.meta;
        const openai = new OpenAI({ apiKey });
        const finetuning = await this.broker.call('finetuning.find', {
          query: {
            isDeleted: false,
            status: {
              $nin: ['succeeded', 'failed', 'canceled']
            }
          }
        });

        const allPromise = finetuning.map(async (fineTuning) => {
          const job = await openai.fineTuning.jobs.retrieve(fineTuning.job.id);
          return this.adapter.updateById(fineTuning._id, {
            ...fineTuning,
            status: job.status,
            job,
            fineTunedModel: job.fine_tuned_model
          });
        });

        return await Promise.all(allPromise);
      }
    }
  },
  methods: {
    getStoragePath() {
      return storageDir;
    },
    async getAPIKey(ctx) {
      const setting = await ctx.call("settings.findOne");
      return ctx.meta.apiKey = setting?.apiKeyOpenAI || process.env.OPENAI_API_KEY;
    },
    async createJsonl(messages) {
      const sampleJsonl = this.getFilePath("sample.jsonl", this.getStoragePath());
      const fileStream = fs.createWriteStream(sampleJsonl);

      for (const obj of messages) {
        fileStream.write(JSON.stringify(obj) + '\n');
      }

      await new Promise((resolve, reject) => {
        fileStream.on('finish', resolve);
        fileStream.on('error', reject);
        fileStream.end();
      });

      return sampleJsonl;
    },
  },

  async started() {
    this.createFolderIfNotExist(storageDir);
  },
  created() {
  }
};
