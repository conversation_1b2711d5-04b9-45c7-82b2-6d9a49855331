const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./knowledge.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");

module.exports = {
  name: 'knowledge',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: []
  },

  hooks: {
    before: {
      update(ctx) {
        ctx.params.isOutDated = true;
      },
    },
  },

  actions: {
    remove: {
      rest: {
        method: "DELETE",
        path: "/:id",
      },
      async handler(ctx) {
        const {id} = ctx.params;
        const knowledgeUpdated = await this.adapter.updateById(id, {isDeleted: true});
        this.broker.emit('knowledgeDeleted', {knowledgeId: id});
        return knowledgeUpdated;
      }
    },
    checkRelated: {
      async handler(ctx) {
        let {instructionEmbedding, knowledge} = ctx.params;
        if (knowledge.isOutDated || !knowledge.embeddingVector || knowledge?.embeddingVector?.length === 0) {
          knowledge = await this.createEmbeddingVector(knowledge);
        }
        const knowledgeEmbeddingVector = knowledge.embeddingVector
        return await this.broker.call('langchain.getRelatedScore', {vectors: [instructionEmbedding, knowledgeEmbeddingVector]});
      }
    },
    findRelatedKnowlegdes: {
      async handler(ctx) {
        let {instruction, optionsKnowledges} = ctx.params;
        const instructionEmbedding = await this.broker.call('langchain.getEmbedding', {textContent: instruction});
        let relatedKnowledges = []
        const checkRelatedKnowledgesTasks = optionsKnowledges?.map(async knowledge => {
          return {
            relateScore: await this.actions.checkRelated({instructionEmbedding, knowledge}),
            knowledge
          }
        })
        relatedKnowledges = await Promise.all(checkRelatedKnowledgesTasks)
        relatedKnowledges = relatedKnowledges.filter(knowledge => knowledge.relateScore > 0.5).sort((k1, k2) => k2.relateScore - k1.relateScore)
        // console.log("instruction and knowledge =================", instruction, relatedKnowledges)
        return relatedKnowledges.map(element => element.knowledge)
      }
    }
  },
  methods: {

    async createEmbeddingVector(knowledge) {
      const embeddingVector = await this.broker.call('langchain.getEmbedding', {textContent: knowledge.content});
      return await this.adapter.updateById(knowledge._id, {embeddingVector, isOutDated: false});
    }
  },
  events: {},

};
