const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./options.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");


module.exports = {
  name: 'options',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "knowledgeIds": 'knowledge.get',
    },
    populateOptions: ["knowledgeIds"]
  },

  hooks: {
    before: {}
  },

  actions: {
    remove: {
      rest: {
        method: "DELETE",
        path: "/:id",
      },
      async handler(ctx) {
        const { id } = ctx.params;
        const instructions = await ctx.call("instructions.find", {query: { optionIds: { $in: [id] }}, populate:[]});
        instructions.map(async instruction => {
          const optionIds = instruction.optionIds.filter(optionId => optionId.toString() !== id);
          await ctx.call("instructions.update", {id: instruction._id, optionIds});
        });
        return this.adapter.updateById(id, { isDeleted: true });
      }
    }
  },
  methods: {},
  events: {
    instructionDeleted: {
      async handler(ctx) {
        const { instructionId } = ctx.params;
        return this.adapter.updateMany({ instructionId }, { isDeleted: true });
      }
    },
  },

};
