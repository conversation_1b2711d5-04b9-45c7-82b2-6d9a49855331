const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./instructions.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const {createMessages} = require("../promptengine");
const AuthRole = require("../../../mixins/authRole.mixin");

module.exports = {
  name: 'instructions',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "optionIds": 'options.get',
      "outputTypeId": 'outputtypes.get',
      "subInstructionIds": 'instructions.get',
      "explainInstructionIds": 'instructions.get',
    },
    populateOptions: ["optionIds", "outputTypeId", "subInstructionIds", "explainInstructionIds"]
  },

  hooks: {
    before: {}
  },

  actions: {
    details: {
      rest: {
        method: "GET",
        path: "/:id/details",
      },
      async handler(ctx) {
        const {id, populateOpts} = ctx.params;

        const [instruction, instructionKnowledge] = await Promise.all([
          this.adapter.findById(id),
          ctx.call('instructionKnowledge.find', {query: {instructionId: id, isDeleted: false}}),
        ]);

        if (!instruction) return null;
        const populate = populateOpts || ["optionIds", "outputTypeId", "subInstructionIds", "explainInstructionIds"];
        const transformedInstruction = await this.transformDocuments(ctx, {populate}, instruction);
        const knowledges = instructionKnowledge.map(k => k.knowledgeId);
        return {...transformedInstruction, options: transformedInstruction.optionIds, knowledges};
      }
    },

    update: {
      rest: {
        method: "PUT",
        path: "/:id",
      },
      async handler(ctx) {
        const {id, knowledges} = ctx.params;
        await this.broker.emit('updateKnowledge', {knowledges, instructionId: id});
        return ctx;
      }
    },

    getPrompt: {
      rest: {
        method: "GET",
        path: "/:id/prompt",
      },
      async handler(ctx) {
        const {id} = ctx.params;

        const instructionData = await this.actions.details({id: id});
        try {
          const messages = await createMessages({
            inputData: {},
            instructionData,
            text: 'INPUT TEXT',
            mockData: 'OPTION VALUE'
          });
          return messages;
        } catch (error) {
          console.log(error);
          return null;
        }
      }
    },

    copy: {
      rest: {
        method: "POST",
        path: "/copy",
      },
      async handler(ctx) {
        const {instructionId} = ctx.params;

        const instruction = await this.adapter.findById(instructionId);

        if (!instruction) {
          throw new Error(`Instruction with ID ${instructionId} not found`);
        }

        const newInstructionObj = {
          ...instruction.toObject(),
          shortName: `${instruction.shortName} - Copy`,
          _id: undefined,
          createdAt: new Date(),
          updatedAt: new Date(),
          isDeleted: false,
        };

        return this.adapter.insert(newInstructionObj);
      }
    },
    remove: {
      rest: {
        method: "DELETE",
        path: "/:id",
      },
      async handler(ctx) {
        const {id} = ctx.params;
        ctx.emit('instructionDeleted', {instructionId: id});
        return await this.adapter.updateById(id, {isDeleted: true});
      }
    },

    getAllManager: {
      rest: {
        method: "GET",
        path: "/manager",
      },
      async handler(ctx) {
        return await this.adapter.find({query: {isDeleted: false}});
      }
    },

    getExplainInstructions: {
      rest: {
        method: "GET",
        path: "/:id/explainInstructions",
      },
      async handler(ctx) {
        const {id} = ctx.params;

        const instruction = await this.adapter.findById(id);
        if (!instruction) {
          throw new Error(`Instruction with ID ${id} not found`);
        }

        const populate = ["explainInstructionIds"];
        const transformedInstruction = await this.transformDocuments(ctx, {populate}, instruction);

        return transformedInstruction.explainInstructionIds || [];
      }
    },
  },
  methods: {},
  events: {},
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {

  },
};
