const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { DATASET, CONVERSATION, USER } = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    datasetId: { type: Schema.Types.ObjectId, ref: DATASET },
    messages: { type: Schema.Types.Mixed },
    creatorId: { type: Schema.Types.ObjectId, ref: USER },
    rating: {
      type: String,
      enum: ["like", "dislike"],
    },
    approved: { type: Boolean, default: false },
    approvedBy: { type: Schema.Types.ObjectId, ref: USER },
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(CONVERSATION, schema, CONVERSATION);
