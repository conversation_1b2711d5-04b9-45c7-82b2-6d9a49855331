"use strict";

const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const explainModel = require("./explain.model");
const {MoleculerClientError} = require("moleculer").Errors;
const {EXPLAIN_TYPES, TASK_TYPES, DIFFICULTY_LEVELS, SECTION_TYPES} = require("./explain.constants");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "explain",
  mixins: [DbMongoose(explainModel), FunctionsCommon, AuthRole, BaseService],

  /**
   * Settings
   */
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    // Enable caching
    cache: {
      enabled: true,
      ttl: 60 * 30 // 30 minutes
    }
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    /**
     * Generate ideas for a topic
     *
     * @param {String} topic - The topic to generate ideas for
     * @param {String} taskType - The task type (task1, task2, general)
     * @param {String} topicImageId - The topic image ID (optional, for Task 1)
     * @returns {Object} The generated ideas
     */
    generateIdea: {
      rest: {
        method: "POST",
        path: "/generateIdea",
      },
      auth: "required",
      params: {
        topic: {type: "string"},
        taskType: {type: "string", optional: true, default: TASK_TYPES.TASK1, enum: Object.values(TASK_TYPES)},
        topicImageId: {type: "string", optional: true}
      },
      async handler(ctx) {
        try {
          const {topic, taskType = TASK_TYPES.TASK1, topicImageId} = ctx.params;
          return await this.handleExplain(ctx, EXPLAIN_TYPES.IDEA, topic, taskType, topicImageId);
        } catch (error) {
          return this.handleError(error, "generateIdea");
        }
      },
    },

    /**
     * Find vocabulary for a topic
     *
     * @param {String} topic - The topic to find vocabulary for
     * @param {String} taskType - The task type (task1, task2, general)
     * @param {String} topicImageId - The topic image ID (optional, for Task 1)
     * @param {String} difficultyLevel - The difficulty level (easy, moderate, hard, expert)
     * @returns {Object} The vocabulary list
     */
    findVocabulary: {
      rest: {
        method: "POST",
        path: "/findVocabulary",
      },
      auth: "required",
      params: {
        topic: {type: "string"},
        taskType: {type: "string", optional: true, default: TASK_TYPES.TASK1, enum: Object.values(TASK_TYPES)},
        topicImageId: {type: "string", optional: true},
        difficultyLevel: {
          type: "string",
          optional: true,
          default: DIFFICULTY_LEVELS.MODERATE,
          enum: Object.values(DIFFICULTY_LEVELS)
        }
      },
      async handler(ctx) {
        try {
          const {
            topic,
            taskType = TASK_TYPES.TASK1,
            topicImageId,
            difficultyLevel = DIFFICULTY_LEVELS.MODERATE
          } = ctx.params;
          return await this.handleExplain(ctx, EXPLAIN_TYPES.FIND_VOCABULARY, topic, taskType, topicImageId, difficultyLevel);
        } catch (error) {
          return this.handleError(error, "findVocabulary");
        }
      },
    },

    /**
     * Help understand a topic for IELTS
     *
     * @param {String} topic - The topic to understand
     * @returns {Object} The explanation
     */
    helpMeUnderstand: {
      rest: {
        method: "POST",
        path: "/helpMeUnderstand",
      },
      auth: "required",
      params: {
        topic: {type: "string"},
        taskType: {type: "string", optional: true, default: TASK_TYPES.TASK1, enum: Object.values(TASK_TYPES)},
        topicImageId: {type: "string", optional: true}
      },
      async handler(ctx) {
        try {
          const {topic, taskType = TASK_TYPES.TASK1, topicImageId} = ctx.params;
          return await this.handleExplain(ctx, EXPLAIN_TYPES.HELP_ME_UNDERSTAND, topic, taskType, topicImageId);
        } catch (error) {
          return this.handleError(error, "helpMeUnderstand");
        }
      },
    },

    /**
     * Help write for a topic for IELTS Task 1
     * @deprecated Use helpMeWrite instead with taskType="task1"
     *
     * @param {String} topic - The topic to write about
     * @returns {Object} The writing help
     */

    helpMeWrite: {
      rest: {
        method: "POST",
        path: "/helpMeWrite",
      },
      auth: "required",
      params: {
        topic: {type: "string"},
        taskType: {type: "string", enum: [TASK_TYPES.TASK1, TASK_TYPES.TASK2]},
        section: {
          type: "string",
          optional: true,
          default: SECTION_TYPES.FULL,
          enum: Object.values(SECTION_TYPES)
        },
        topicImageId: {type: "string", optional: true},
        practiceContent: {
          type: "object",
          optional: true,
          properties: {
            introduction: {type: "string", optional: true},
            overview: {type: "string", optional: true},
            body: {type: "string", optional: true},
            conclusion: {type: "string", optional: true}
          }
        }
      },
      async handler(ctx) {
        try {
          const {topic, taskType, section = SECTION_TYPES.FULL, topicImageId, practiceContent} = ctx.params;

          return await this.handleExplain(
            ctx,
            EXPLAIN_TYPES.HELP_ME_WRITE,
            topic,
            taskType,
            topicImageId,
            null, // Difficulty level không áp dụng cho help_me_write
            section
          );
        } catch (error) {
          return this.handleError(error, "helpMeWrite");
        }
      },
    },

    /**
     * Get explain by explainType
     *
     * @param {String} explainType - The type of explanation to retrieve
     * @param {String} taskType - The task type (optional, default: "general")
     * @returns {Object} The explain data
     */
    getExplainByType: {
      rest: {
        method: "GET",
        path: "/getExplainByType",
      },
      auth: "required",
      async handler(ctx) {
        try {
          const {explainType, taskType = "general"} = ctx.params;

          // Tìm explain data trong database
          const explainData = await this.adapter.findOne({
            explainType,
            taskType,
            isDeleted: false
          });

          if (!explainData) {
            throw new MoleculerClientError(
              `Không tìm thấy explain cho loại: ${explainType} và task: ${taskType}`,
              404,
              "EXPLAIN_NOT_FOUND"
            );
          }

          return explainData
        } catch (error) {
          return this.handleError(error, "getExplainByType");
        }
      },
    },

    /**
     * Get all available explain types
     *
     * @param {String} taskType - The task type to filter by (optional)
     * @returns {Object} List of available explain types
     */
    getAllExplainTypes: {
      rest: {
        method: "GET",
        path: "/getAllExplainTypes",
      },
      auth: "required",
      params: {
        taskType: {
          type: "string",
          optional: true,
          enum: ["task1", "task2", "general", "speaking_room"]
        }
      },
      async handler(ctx) {
        try {
          const {taskType} = ctx.params;

          // Tạo query filter
          const filter = {isDeleted: false};
          if (taskType) {
            filter.taskType = taskType;
          }

          // Lấy tất cả explain data
          const explainList = await this.adapter.find({
            query: filter,
            sort: "explainType taskType"
          });

          // Nhóm theo explainType
          const groupedByType = explainList.reduce((acc, item) => {
            if (!acc[item.explainType]) {
              acc[item.explainType] = [];
            }
            acc[item.explainType].push({
              id: item._id,
              name: item.name,
              taskType: item.taskType,
              speakingRoomType: item.speakingRoomType,
              responseFormat: item.responseFormat,
              gptModel: item.gptModel
            });
            return acc;
          }, {});

          return {
            success: true,
            data: {
              availableTypes: Object.keys(EXPLAIN_TYPES),
              explainsByType: groupedByType,
              totalCount: explainList.length
            }
          };
        } catch (error) {
          return this.handleError(error, "getAllExplainTypes");
        }
      },
    },

    /**
     * Seed initial data for explain
     */
  },

  /**
   * Events
   */
  events: {
    explainConnected: {
      async handler(socket) {
        const connectionId = socket.id;
        this.logger.info(`Explain client connected: ${connectionId}`);

        // Lưu trữ thông tin kết nối
        if (!this.connectionState) {
          this.connectionState = {};
        }

        // Sử dụng giá trị mặc định cho thông tin người dùng
        this.connectionState[connectionId] = {
          userId: "default-user",
          userFullName: "User",
          lang: "vi" // Mặc định sử dụng tiếng Việt
        };

        // Xử lý sự kiện generateIdea
        socket.on("generateIdea", async (data) => {
          try {
            const {topic, taskType, topicImageId} = data;
            const state = this.connectionState[connectionId];

            // Tạo context giả lập
            const ctx = {
              params: {topic, taskType, topicImageId},
              meta: {
                userID: state.userId,
                user: {fullName: state.userFullName},
                lang: state.lang
              }
            };

            // Gọi handler xử lý
            const result = await this.handleExplain(ctx, EXPLAIN_TYPES.IDEA, topic, taskType, topicImageId);

            // Trả kết quả về client
            socket.emit("generateIdea_result", result);
          } catch (error) {
            this.logger.error("Generate idea error:", error);
            socket.emit("generateIdea_result", this.handleError(error, "generateIdea"));
          }
        });

        // Xử lý sự kiện findVocabulary
        socket.on("findVocabulary", async (data) => {
          try {
            const {topic, taskType, topicImageId, difficultyLevel} = data;
            const state = this.connectionState[connectionId];

            // Tạo context giả lập
            const ctx = {
              params: {topic, taskType, topicImageId, difficultyLevel},
              meta: {
                userID: state.userId,
                user: {fullName: state.userFullName},
                lang: state.lang
              }
            };

            // Gọi handler xử lý
            const result = await this.handleExplain(ctx, EXPLAIN_TYPES.FIND_VOCABULARY, topic, taskType, topicImageId, difficultyLevel);

            // Trả kết quả về client
            socket.emit("findVocabulary_result", result);
          } catch (error) {
            this.logger.error("Find vocabulary error:", error);
            socket.emit("findVocabulary_result", this.handleError(error, "findVocabulary"));
          }
        });

        // Xử lý sự kiện helpMeUnderstand
        socket.on("helpMeUnderstand", async (data) => {
          try {
            const {topic, taskType, topicImageId} = data;
            const state = this.connectionState[connectionId];

            // Tạo context giả lập
            const ctx = {
              params: {topic, taskType, topicImageId},
              meta: {
                userID: state.userId,
                user: {fullName: state.userFullName},
                lang: state.lang
              }
            };

            // Gọi handler xử lý
            const result = await this.handleExplain(ctx, EXPLAIN_TYPES.HELP_ME_UNDERSTAND, topic, taskType, topicImageId);

            // Trả kết quả về client
            socket.emit("helpMeUnderstand_result", result);
          } catch (error) {
            this.logger.error("Help me understand error:", error);
            socket.emit("helpMeUnderstand_result", this.handleError(error, "helpMeUnderstand"));
          }
        });

        // Xử lý sự kiện helpMeWrite
        socket.on("helpMeWrite", async (data) => {
          try {
            const {topic, taskType, section, topicImageId, practiceContent} = data;
            const state = this.connectionState[connectionId];

            // Tạo context giả lập
            const ctx = {
              params: {topic, taskType, section, topicImageId, practiceContent},
              meta: {
                userID: state.userId,
                user: {fullName: state.userFullName},
                lang: state.lang
              }
            };

            // Gọi handler xử lý
            const result = await this.handleExplain(
              ctx,
              EXPLAIN_TYPES.HELP_ME_WRITE,
              topic,
              taskType,
              topicImageId,
              null,
              section
            );

            // Trả kết quả về client
            socket.emit("helpMeWrite_result", result);
          } catch (error) {
            this.logger.error("Help me write error:", error);
            socket.emit("helpMeWrite_result", this.handleError(error, "helpMeWrite"));
          }
        });

        // Xử lý sự kiện ngắt kết nối
        socket.on("disconnect", () => {
          this.logger.info(`Explain client disconnected: ${connectionId}`);
          if (this.connectionState && this.connectionState[connectionId]) {
            delete this.connectionState[connectionId];
          }
        });

        // Thông báo cho client rằng server đã sẵn sàng
        socket.emit("server_ready");
      }
    }
  },

  /**
   * Methods
   */
  methods: {
    /**
     * Xử lý lỗi và trả về response chuẩn hóa
     *
     * @param {Error} error - Lỗi cần xử lý
     * @param {String} methodName - Tên phương thức gặp lỗi
     * @returns {Object} Response chuẩn hóa
     */
    handleError(error, methodName) {
      this.logger.error(`Error in ${methodName}:`, error);

      if (error instanceof MoleculerClientError) {
        return {
          success: false,
          error: {
            message: error.message,
            code: error.code || error.type,
            data: error.data
          }
        };
      }

      return {
        success: false,
        error: {
          message: error.message || "An unexpected error occurred",
          code: "UNKNOWN_ERROR"
        }
      };
    },

    /**
     * Tạo hướng dẫn tùy chỉnh dựa trên các tham số
     *
     * @param {Object} explainData - Dữ liệu explain từ database
     * @param {String} explainType - Loại explain
     * @param {String} difficultyLevel - Cấp độ khó (cho find_vocabulary)
     * @param {String} section - Phần cụ thể của bài viết (cho help_me_write)
     * @returns {String} Hướng dẫn đã được tùy chỉnh
     */
    createCustomizedInstruction(ctx, explainData, explainType, difficultyLevel = DIFFICULTY_LEVELS.MODERATE, section = SECTION_TYPES.FULL) {
      let customizedInstruction = explainData.instruction;

      // Tùy chỉnh theo cấp độ khó cho find_vocabulary
      if (explainType === EXPLAIN_TYPES.FIND_VOCABULARY) {
        const difficultyInstructions = {
          [DIFFICULTY_LEVELS.EASY]: "**Easy:** Từ vựng thông dụng, nền tảng, dễ hiểu, thường gặp trong giao tiếp hàng ngày và các bài viết đơn giản.",
          [DIFFICULTY_LEVELS.MODERATE]: "**Moderate:** Từ vựng phổ biến hơn trong văn phong học thuật hoặc các cuộc thảo luận chuyên sâu hơn một chút, có thể không quá quen thuộc với người học trình độ cơ bản.",
          [DIFFICULTY_LEVELS.HARD]: "**Hard:** Từ vựng học thuật hoặc chuyên biệt hơn, ít gặp trong giao tiếp thông thường, đòi hỏi vốn từ rộng hơn để hiểu và sử dụng chính xác.",
          [DIFFICULTY_LEVELS.EXPERT]: "**Expert:** Từ vựng nâng cao, ít phổ biến, có thể mang sắc thái nghĩa tinh tế hoặc được sử dụng trong các ngữ cảnh rất trang trọng/học thuật chuyên sâu. Việc sử dụng chính xác các từ này thể hiện trình độ ngôn ngữ rất cao."
        };

        customizedInstruction = `${explainData.instruction}\n\n **Cấp độ khó từ vựng:** ${difficultyInstructions[difficultyLevel]}`;
      }

      // Tùy chỉnh theo phần bài viết cho help_me_write
      if (explainType === EXPLAIN_TYPES.HELP_ME_WRITE) {
        // Lấy các phần văn bản đã viết (nếu có)
        const practiceContent = ctx.params.practiceContent || {};
        const existingParts = [];

        // Thêm các phần đã viết vào instruction
        if (practiceContent.introduction) {
          customizedInstruction = `${customizedInstruction}\n\n**Đây là đoạn giới thiệu đã viết:** ${practiceContent.introduction}`;
          existingParts.push("introduction");
        }

        if (practiceContent.overview) {
          customizedInstruction = `${customizedInstruction}\n\n**Đây là đoạn tóm tắt đã viết:** ${practiceContent.overview}`;
          existingParts.push("overview");
        }

        if (practiceContent.body) {
          customizedInstruction = `${customizedInstruction}\n\n**Đây là đoạn thân bài đã viết:** ${practiceContent.body}`;
          existingParts.push("body");
        }

        if (practiceContent.conclusion) {
          customizedInstruction = `${customizedInstruction}\n\n**Đây là đoạn kết luận đã viết:** ${practiceContent.conclusion}`;
          existingParts.push("conclusion");
        }

        // Nếu không phải viết toàn bộ bài, thêm hướng dẫn cụ thể cho phần cần viết
        if (section !== SECTION_TYPES.FULL) {
          const sectionInstructions = {
            [SECTION_TYPES.INTRODUCTION]: "Vui lòng viết phần giới thiệu hiệu quả cho chủ đề này. Bao gồm phần diễn giải lại câu hỏi, câu luận đề rõ ràng và dàn ý về những gì sẽ được thảo luận trong bài luận.",
            [SECTION_TYPES.OVERVIEW]: "Tạo một đoạn văn tóm tắt 2-3 xu hướng chính hoặc các tính năng chính chung từ dữ liệu/hình ảnh.",
            [SECTION_TYPES.BODY]: "Vui lòng viết các đoạn văn thân bài cho chủ đề này. Bao gồm các lập luận được phát triển tốt, bằng chứng hỗ trợ và ví dụ. Mỗi đoạn văn phải có câu chủ đề rõ ràng và mạch lạc.",
            [SECTION_TYPES.CONCLUSION]: "Vui lòng viết một kết luận mạnh mẽ cho chủ đề này. Tóm tắt các điểm chính, nêu lại luận điểm bằng các từ khác nhau và kết thúc bằng một suy nghĩ hoặc khuyến nghị cuối cùng."
          };

          // Thêm hướng dẫn cho phần cần viết
          customizedInstruction = `${customizedInstruction}\n\n${sectionInstructions[section]}`;

          // Thêm hướng dẫn về việc kết hợp với các phần đã viết
          if (existingParts.length > 0) {
            customizedInstruction = `${customizedInstruction}\n\n**Hướng dẫn kết hợp:** Hãy đảm bảo phần ${section} bạn viết phù hợp và kết nối mạch lạc với các phần đã viết trước đó. Duy trì tính nhất quán về giọng điệu, phong cách và nội dung.`;

            // Thêm hướng dẫn cụ thể dựa trên phần cần viết và các phần đã có
            if (section === SECTION_TYPES.INTRODUCTION && (existingParts.includes("body") || existingParts.includes("conclusion"))) {
              customizedInstruction = `${customizedInstruction} Phần giới thiệu cần giới thiệu các ý chính đã được phát triển trong phần thân bài.`;
            } else if (section === SECTION_TYPES.BODY) {
              if (existingParts.includes("introduction")) {
                customizedInstruction = `${customizedInstruction} Phần thân bài cần phát triển các ý đã được giới thiệu trong phần mở đầu.`;
              }
              if (existingParts.includes("conclusion")) {
                customizedInstruction = `${customizedInstruction} Đảm bảo phần thân bài cung cấp đủ nội dung để hỗ trợ cho kết luận đã viết.`;
              }
            } else if (section === SECTION_TYPES.CONCLUSION && (existingParts.includes("introduction") || existingParts.includes("body"))) {
              customizedInstruction = `${customizedInstruction} Kết luận cần tóm tắt và kết nối với các ý chính đã được trình bày trong phần thân bài và giới thiệu.`;
            } else if (section === SECTION_TYPES.OVERVIEW && existingParts.includes("introduction")) {
              customizedInstruction = `${customizedInstruction} Phần tóm tắt cần phù hợp với nội dung đã giới thiệu.`;
            }
          }
        }
        // Nếu viết toàn bộ bài nhưng đã có một số phần
        else if (existingParts.length > 0) {
          customizedInstruction = `${customizedInstruction}\n\n**Hướng dẫn viết toàn bộ bài:** Hãy viết các phần còn thiếu để hoàn thiện bài viết, đảm bảo kết nối mạch lạc với các phần đã viết. Các phần cần viết thêm: ${["introduction", "overview", "body", "conclusion"].filter(part => !existingParts.includes(part)).join(", ")}.`;
        }
      }

      return customizedInstruction;
    },

    /**
     * Tạo messages cho LLM
     *
     * @param {String} topic - Chủ đề
     * @param {String} customizedInstruction - Hướng dẫn đã được tùy chỉnh
     * @param {String} topicImageBase64 - Hình ảnh dạng base64 (nếu có)
     * @param {String} outputLanguage - Ngôn ngữ đầu ra
     * @param {String} userFullName - Tên người dùng
     * @param {String} systemPrompt - System prompt (nếu có)
     * @returns {Array} Mảng các messages cho LLM
     */
    createMessages(topic, customizedInstruction, topicImageBase64 = null, outputLanguage, userFullName, systemPrompt = null) {
      const messages = [];

      // Thêm system prompt nếu có
      if (systemPrompt) {
        messages.push({
          role: "system",
          content: systemPrompt
        });
      }

      if (topicImageBase64) {
        messages.push({
          role: "user",
          content: [
            {type: "text", text: `${customizedInstruction}\n\nTopic: ${topic}`},
            {
              type: "image_url",
              image_url: {
                url: `data:image/png;base64,${topicImageBase64}`
              }
            }
          ]
        });
      } else {
        messages.push({
          role: "user",
          content: `${customizedInstruction}\n\nTopic: ${topic}`
        });
      }

      messages.push({
        role: "user",
        content: `Ngôn ngữ đầu ra tổng quan: ${outputLanguage}(giữ lại Tiếng Anh cho các phần được yêu cầu) và bắt đầu bằng "Oke ${userFullName}"`
      });

      return messages;
    },
    /**
     * Handle explain request
     *
     * @param {Context} ctx - The context
     * @param {String} explainType - The type of explanation
     * @param {String} topic - The topic
     * @param {String} taskType - The task type (optional)
     * @param {String} topicImageId - The topic image ID (optional, for Task 1)
     * @param {String} difficultyLevel - The difficulty level (easy, moderate, hard, expert)
     * @param {String} section - The specific section for writing help (introduction, body, conclusion, full)
     * @returns {Object} The explanation result
     */
    async handleExplain(ctx, explainType, topic, taskType = TASK_TYPES.TASK1, topicImageId = null, difficultyLevel = DIFFICULTY_LEVELS.MODERATE, section = SECTION_TYPES.FULL) {
      try {
        const {lang, user} = ctx.meta;
        const outputLanguage = lang === "vi" ? "Vietnamese" : "English";
        const userFullName = user?.fullName || "User";

        const explainData = await this.adapter.findOne({explainType, taskType, isDeleted: false});

        if (!explainData) {
          throw new MoleculerClientError(`No explain found for type: ${explainType} and task: ${taskType}`, 404, "EXPLAIN_NOT_FOUND");
        }

        // Create customized instruction
        const customizedInstruction = this.createCustomizedInstruction(ctx, explainData, explainType, difficultyLevel, section);

        // Handle image for Task 1 if topicImageId is provided
        let topicImageBase64 = null;
        if (topicImageId && taskType === TASK_TYPES.TASK1) {
          try {
            // Get image data
            topicImageBase64 = await ctx.call("images.getBase64", {imageId: topicImageId.toString()});
          } catch (error) {
            this.logger.error("Error getting image data:", error);
            // Continue without image
          }
        }

        // Create messages
        const messages = this.createMessages(topic, customizedInstruction, topicImageBase64, outputLanguage, userFullName, explainData.systemPrompt);

        // Prepare response format and schema
        const responseFormat = explainData.responseFormat || "text";
        const schema = explainData.schemaInstruction || null;

        // Call LLM with rate limiting
        const aiResult = await ctx.call("tools.submitFastLLM", {
          messages,
          schema,
          temperature: explainData.temperature || 0.7,
          max_tokens: explainData.maxTokens || 1000,
          responseFormat
        });
        return {
          success: true,
          result: aiResult,
        };
      } catch (error) {
        return this.handleError(error, "handleExplain");
      }
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
